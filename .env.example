TOKEN="" # Your bot token.
CLIENT_ID="" # Your bot's client ID (If this value is left blank, bots cannot be invited using /invite or /about commands.).
DEFAULT_LANGUAGE="EnglishUS" # Default language for bot
PREFIX="!" # Your prefix.
OWNER_IDS=["",""] # Your discord id (You can add multiple ids.).
GUILD_ID="" # Your server ID (If you want to use the bot for a single server).
TOPGG="" # Your Top.gg API key. Obtain this from https://top.gg
KEEP_ALIVE="false" # true for keep alive in https://replit.com
LOG_CHANNEL_ID="" # If you enter this, you will be able to receive the status of Lavalink nodes and guild join/leave logs through the corresponding channel.
LOG_COMMANDS_ID="" # The channel ID where command usage logs will be sent.
BOT_STATUS="online" # Your bot status (online, dnd, idle, invisible or offline).
BOT_ACTIVITY_TYPE=0 # Activity type is a number from 0 to 5. See more here: https://discord.com/developers/docs/topics/gateway-events#activity-object-activity-types
BOT_ACTIVITY="Lavamusic" # Your bot activity.
DATABASE_URL="" # Your database url (If you want to use sqlite, then you can leave it blank.).
AUTO_NODE="false" # true for auto node. It is given from lavainfo-api (https://lavainfo-api.deno.dev).
SEARCH_ENGINE="YouTubeMusic" # Search engine to be used when playing the song. You can use: YouTube, YouTubeMusic, SoundCloud, Spotify, Apple, Deezer, Yandex and JioSaavn
GENIUS_API="" # Sign up and get your own api at (https://genius.com/) to fetch your lyrics (CLIENT TOKEN)
NODES=[{"id":"Local Node","host":"lavalink","port":2333,"authorization":"youshallnotpass","retryAmount":5,"retryDelay":60000,"secure":"false"}]
