{"cmd": {"247": {"description": "Set the bot to stay in the voice channel", "errors": {"not_in_voice": "You need to be in a voice channel to use this command.", "generic": "An error occurred while trying to execute this command."}, "messages": {"disabled": "`✅` | 24/7 mode has been `DISABLED`.", "enabled": "`✅` | 24/7 mode has been `ENABLED`."}}, "fairplay": {"description": "Set the bot to play music fairly", "errors": {"not_in_voice": "You need to be in a voice channel to use this command.", "generic": "An error occurred while trying to execute this command."}, "messages": {"disabled": "`✅` | Fairplay mode has been `DISABLED`.", "enabled": "`✅` | Fairplay mode has been `ENABLED`."}}, "ping": {"description": "Shows the bot's ping.", "content": "Pinging...", "bot_latency": "<PERSON><PERSON> Latency", "api_latency": "API Latency", "requested_by": "Requested by {author}"}, "lavalink": {"description": "Shows the current Lavalink stats.", "title": "Lavalink Stats", "content": "Player: {players}\nPlaying Players: {playingPlayers}\nUptime: {uptime}\nCores: {cores} Core(s)\nMemory Usage: {used} / {reservable}\nSystem Load: {systemLoad}%\nLavalink Load: {lavalinkLoad}%", "page_info": "Page {index} of {total}"}, "invite": {"description": "Get the bot invite link.", "content": "You can invite me by clicking the button below. Any bugs or outages? Join the support server!"}, "help": {"description": "Shows the help menu.", "options": {"command": "The command you want to get info on"}, "content": "Hey there! I'm {bot}, a music bot made with [Lavamusic](https://github.com/appujet/lavamusic) and Discord.js. You can use `{prefix}help <command>` to get more info on a command.", "title": "Help Menu", "not_found": "This `{cmdName}` command does not exist.", "help_cmd": "**Description:** {description}\n**Usage:** {usage}\n**Examples:** {examples}\n**Aliases:** {aliases}\n**Category:** {category}\n**Cooldown:** {cooldown} seconds\n**Permissions:** {premUser}\n**Bot Permissions:** {premBot}\n**Developer Only:** {dev}\n**Slash Command:** {slash}\n**Args:** {args}\n**Player:** {player}\n**DJ:** {dj}\n**DJ Permissions:** {djPerm}\n**Voice:** {voice}", "footer": "Use {prefix}help <command> for more info on a command"}, "botinfo": {"description": "Information about the bot", "content": "Bot Information:\n- **Operating System**: {osInfo}\n- **Uptime**: {osUptime}\n- **Hostname**: {osHostname}\n- **CPU Architecture**: {cpuInfo}\n- **CPU Usage**: {cpuUsed}%\n- **Memory Usage**: {memUsed}MB / {memTotal}GB\n- **Node Version**: {nodeVersion}\n- **Discord.js Version**: {discordJsVersion}\n- **Connected to** {guilds} guilds, {channels} channels, and {users} users\n- **Total Commands**: {commands}"}, "about": {"description": "Shows information about the bot", "fields": {"creator": "Creator", "repository": "Repository", "support": "Support", "description": "He really wanted to make his first open source project ever for more coding experience. In this project, he was challenged to make a project with fewer bugs. Hope you enjoy using LavaMusic!"}}, "dj": {"description": "Manage the DJ mode and associated roles", "errors": {"provide_role": "Please provide a role.", "no_roles": "The DJ role is empty.", "invalid_subcommand": "Please provide a valid subcommand."}, "messages": {"role_exists": "The DJ role <@&{roleId}> is already added.", "role_added": "The DJ role <@&{roleId}> has been added.", "role_not_found": "The DJ role <@&{roleId}> is not added.", "role_removed": "The DJ role <@&{roleId}> has been removed.", "all_roles_cleared": "All DJ roles have been removed.", "toggle": "The DJ mode has been toggled to {status}."}, "options": {"add": "The DJ role you want to add", "remove": "The DJ role you want to remove", "clear": "Clears all DJ roles", "toggle": "Toggles the DJ role", "role": "The DJ role"}, "subcommands": "Subcommands"}, "language": {"description": "Set the language for the bot", "invalid_language": "Please provide a valid language. Example: `EnglishUS` for English (United States)\n\nYou can find the list of supported languages [here](https://discord.com/developers/docs/reference#locales)\n\n**Available Languages:**\n{languages}", "already_set": "The language is already set to `{language}`", "not_set": "The language is not set.", "set": "`✅` | The language has been set to `{language}`", "reset": "`✅` | The language has been reset to the default language.", "options": {"set": "Set the language for the bot", "language": "The language you want to set", "reset": "Change the language back to the default language"}}, "prefix": {"description": "Shows or sets the bot's prefix", "errors": {"prefix_too_long": "The prefix cannot be longer than 3 characters."}, "messages": {"current_prefix": "The prefix for this server is `{prefix}`", "prefix_set": "The prefix for this server is now `{prefix}`", "prefix_reset": "The prefix for this server is now `{prefix}`"}, "options": {"set": "Sets the prefix", "prefix": "The prefix you want to set", "reset": "Resets the prefix to the default one"}}, "setup": {"description": "Sets up the bot", "errors": {"channel_exists": "The song request channel already exists.", "channel_not_exists": "The song request channel doesn't exist.", "channel_delete_fail": "The setup channel has been deleted from the database. Please delete the channel yourself."}, "messages": {"channel_created": "The song request channel has been created in <#{channelId}>.", "channel_deleted": "The song request channel has been deleted.", "channel_info": "The song request channel is <#{channelId}>."}, "options": {"create": "Creates the song request channel", "delete": "Deletes the song request channel", "info": "Shows the song request channel"}}, "8d": {"description": "on/off 8d filter", "messages": {"filter_enabled": "`✅` | 8D filter has been `ENABLED`.", "filter_disabled": "`✅` | 8D filter has been `DISABLED`."}}, "bassboost": {"description": "on/off bassboost filter", "options": {"level": "The bassboost level you want to set"}, "messages": {"high": "`✅` | High bassboost filter has been `ENABLED`.", "low": "`✅` | Low bassboost filter has been `ENABLED`.", "medium": "`✅` | Medium bassboost filter has been `ENABLED`.", "off": "`✅` | Bassboost filter has been `DISABLED`."}}, "distortion": {"description": "Toggle the distortion filter on/off", "messages": {"filter_enabled": "`✅` | Distortion filter has been `ENABLED`.", "filter_disabled": "`✅` | Distortion filter has been `DISABLED`."}}, "karaoke": {"description": "Toggle the karaoke filter on/off", "messages": {"filter_enabled": "`✅` | Karaoke filter has been `ENABLED`.", "filter_disabled": "`✅` | Karaoke filter has been `DISABLED`."}}, "lowpass": {"description": "Toggle the lowpass filter on/off", "messages": {"filter_enabled": "`✅` | Lowpass filter has been `ENABLED`.", "filter_disabled": "`✅` | Lowpass filter has been `DISABLED`."}}, "nightcore": {"description": "Toggle the nightcore filter on/off", "messages": {"filter_enabled": "`✅` | Nightcore filter has been `ENABLED`.", "filter_disabled": "`✅` | Nightcore filter has been `DISABLED`."}}, "pitch": {"description": "Toggle the pitch filter on/off", "options": {"pitch": "The number you want to set the pitch to (between 0.5 and 5)"}, "errors": {"invalid_number": "Please provide a valid number between 0.5 and 5."}, "messages": {"pitch_set": "`✅` | Pitch has been set to **{pitch}**."}}, "rate": {"description": "Change the rate of the song", "options": {"rate": "The number you want to set the rate to (between 0.5 and 5)"}, "errors": {"invalid_number": "Please provide a valid number between 0.5 and 5."}, "messages": {"rate_set": "`✅` | Rate has been set to **{rate}**."}}, "reset": {"description": "Resets the active filters", "messages": {"filters_reset": "`✅` | Filters have been reset."}}, "rotation": {"description": "Toggle the rotation filter on/off", "messages": {"enabled": "`✅` | Rotation filter has been `ENABLED`.", "disabled": "`✅` | Rotation filter has been `DISABLED`."}}, "speed": {"description": "Change the speed of the song", "options": {"speed": "The speed you want to set"}, "messages": {"invalid_number": "Please provide a valid number between 0.5 and 5.", "set_speed": "`✅` | Speed has been set to **{speed}**."}}, "tremolo": {"description": "Toggle the tremolo filter on/off", "messages": {"enabled": "`✅` | Tremolo filter has been `ENABLED`.", "disabled": "`✅` | Tremolo filter has been `DISABLED`."}}, "vibrato": {"description": "Toggle the vibrato filter on/off", "messages": {"enabled": "`✅` | Vibrato filter has been `ENABLED`.", "disabled": "`✅` | Vibrato filter has been `DISABLED`."}}, "autoplay": {"description": "Toggles autoplay", "messages": {"enabled": "`✅` | Autoplay has been `ENABLED`.", "disabled": "`✅` | Autoplay has been `DISABLED`."}}, "clearqueue": {"description": "Clears the queue", "messages": {"cleared": "The queue has been cleared."}}, "grab": {"description": "Grabs the current playing song on your DM", "loading": "Loading...", "content": "**Duration:** {length}\n**Requested by:** <@{requester}>\n**Link:** [Click here]({uri})", "check_dm": "Please check your DM.", "dm_failed": "I couldn't send you a DM. Please make sure allow direct messages is turned on."}, "join": {"description": "Joins the voice channel", "already_connected": "I'm already connected to <#{channelId}>.", "no_voice_channel": "You need to be in a voice channel to use this command.", "joined": "Successfully joined <#{channelId}>."}, "leave": {"description": "Leaves the voice channel", "left": "Successfully left <#{channelId}>.", "not_in_channel": "I'm not in a voice channel."}, "loop": {"description": "Loop the current song or the queue", "looping_song": "**Looping the song.**", "looping_queue": "**Looping the queue.**", "looping_off": "**Looping is now off.**"}, "lyrics": {"description": "Get's the lyrics of the currently playing track", "lyrics_track": "### Lyrics for: [{trackTitle}]({trackUrl})\n**`{lyrics}`**", "searching": "`🔍` Searching for **{trackTitle}** lyrics...", "errors": {"no_results": "No lyrics found for the current track.", "lyrics_error": "An error occurred while getting the lyrics."}}, "nowplaying": {"description": "Shows the currently playing song", "now_playing": "Now Playing", "track_info": "[{title}]({uri}) - Requested By: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "Pauses the current song", "successfully_paused": "Successfully paused the song."}, "play": {"description": "Plays a song from YouTube, Spotify or http", "options": {"song": "The song you want to play"}, "loading": "Loading...", "errors": {"search_error": "There was an error while searching.", "no_results": "There were no results found.", "queue_too_long": "The queue is too long. The maximum length is {maxQueueSize} songs.", "playlist_too_long": "The playlist is too long. The maximum length is {maxPlaylistSize} songs."}, "added_to_queue": "Added [{title}]({uri}) to the queue.", "added_playlist_to_queue": "Added {length} songs to the queue."}, "playlocal": {"description": "Plays an your local audio files", "options": {"file": "The audio file you want to play"}, "loading": "Loading...", "errors": {"empty_query": "Please attach an audio file to play.", "invalid_format": "You can only play MP3, WAV, OGG, FLAC, AAC or M4A files.", "no_results": "Failed to play the audio file. It may be corrupted."}, "added_to_queue": "Added [{title}]({url}) to the queue"}, "playnext": {"description": "Add the song to play next in queue", "options": {"song": "The song you want to play"}, "loading": "Loading...", "errors": {"search_error": "There was an error while searching.", "no_results": "There were no results found.", "queue_too_long": "The queue is too long. The maximum length is {maxQueueSize} songs.", "playlist_too_long": "The playlist is too long. The maximum length is {maxPlaylistSize} songs."}, "added_to_play_next": "Added [{title}]({uri}) to play next in the queue.", "added_playlist_to_play_next": "Added {length} songs to play next in the queue."}, "queue": {"description": "Shows the current queue", "now_playing": "Now playing: [{title}]({uri}) - Requested By: <@{requester}> - Duration: `{duration}`", "live": "LIVE", "track_info": "{index}. [{title}]({uri}) - Requested By: <@{requester}> - Duration: `{duration}`", "title": "Queue", "page_info": "Page {index} of {total}", "duration": "Queue Duration: `{totalDuration}`"}, "remove": {"description": "Removes a song from the queue", "options": {"song": "The song number you want to remove"}, "errors": {"no_songs": "There are no songs in the queue.", "invalid_number": "Please provide a valid song number."}, "messages": {"removed": "Removed song number {songNumber} from the queue."}}, "replay": {"description": "Replays the current track", "errors": {"not_seekable": "Cannot replay this track as it is not seekable."}, "messages": {"replaying": "Replaying the current track."}}, "resume": {"description": "Resumes the current song", "errors": {"not_paused": "The player is not paused."}, "messages": {"resumed": "Resumed the player."}}, "search": {"description": "Searches for a song", "options": {"song": "The song you want to search"}, "select": "Please select the song you want to play", "errors": {"no_results": "No results found.", "search_error": "There was an error while searching."}, "messages": {"added_to_queue": "Added [{title}]({uri}) to the queue."}}, "seek": {"description": "Seeks to a certain time in the song", "options": {"duration": "The duration to seek to"}, "errors": {"invalid_format": "Invalid time format. Examples: seek 1m, seek 1h 30m", "not_seekable": "This track is not seekable.", "beyond_duration": "Cannot seek beyond the song duration of {length}."}, "messages": {"seeked_to": "Seeked to {duration}"}}, "shuffle": {"description": "Shuffles the queue", "messages": {"shuffled": "Shuffled the queue."}, "errors": {"fairplay": "Fairplay mode is enabled. You cannot shuffle the queue."}}, "skip": {"description": "Skips the current song", "messages": {"skipped": "Skipped [{title}]({uri})."}}, "skipto": {"description": "Skips to a specific song in the queue", "options": {"number": "The number of the song you want to skip to"}, "errors": {"invalid_number": "Please provide a valid number."}, "messages": {"skipped_to": "Skipped to song number {number}."}}, "stop": {"description": "Stops the music and clears the queue", "messages": {"stopped": "Stopped the music and cleared the queue."}}, "volume": {"description": "Sets the volume of the player", "options": {"number": "The volume you want to set"}, "messages": {"invalid_number": "Please provide a valid number.", "too_low": "The volume can't be lower than 0.", "too_high": "The volume can't be higher than 200. Do you want to damage your hearing or speakers? Hmmm, I don't think that's such a good idea.", "set": "Set the volume to {volume}"}}, "speak": {"description": "Convert text to speech with advanced voice options", "options": {"text": "The text to convert to speech", "voice": "The voice to use for speech synthesis", "speed": "The speed of speech (0.5 to 2.0)"}, "loading": "🎤 Converting text to speech...", "success": "🎤 **Speaking:** {text}\n**Voice:** {voice}", "errors": {"no_text": "Please provide text to convert to speech.", "text_too_long": "Text is too long! Maximum 500 characters allowed.", "tts_failed": "Failed to convert text to speech. Please try again."}, "fields": {"voice": "Voice", "speed": "Speed", "length": "Text Length"}}, "say": {"description": "Quick text to speech conversion", "options": {"text": "The text to say"}, "loading": "🗣️ Converting text to speech...", "success": "🗣️ **Said:** {text}", "errors": {"no_text": "Please provide text to say.", "text_too_long": "Text is too long! Maximum 200 characters allowed.", "tts_failed": "Failed to convert text to speech. Please try again."}, "fields": {"text_length": "Text Length", "voice": "Voice Type"}}, "mluvit": {"description": "Czech text to speech conversion", "options": {"text": "Text to convert to Czech speech", "voice": "Czech voice to use (<PERSON><PERSON><PERSON> or <PERSON><PERSON>)", "speed": "Speech speed (0.5 to 2.0)"}, "loading": "🇨🇿 Převádím text na řeč...", "success": "🇨🇿 **Mluvím:** {text}\n**Hlas:** {voice}", "errors": {"no_text": "Prosím zadejte text k převedení na řeč.", "text_too_long": "Text je p<PERSON><PERSON><PERSON> d<PERSON>! Maximum 500 znaků.", "tts_failed": "Nepodařilo se převést text na řeč. Zkuste to znovu."}, "fields": {"voice": "<PERSON><PERSON>", "speed": "Rychlost", "length": "<PERSON><PERSON><PERSON><PERSON> textu"}}, "addsong": {"description": "Adds a song to the playlist", "options": {"playlist": "The playlist you want to add", "song": "The song you want to add"}, "messages": {"no_playlist": "Please provide a playlist", "no_song": "Please provide a song", "playlist_not_found": "That playlist doesn't exist", "no_songs_found": "No songs found", "added": "Added {count} song(s) to {playlist}"}}, "create": {"description": "Creates a playlist", "options": {"name": "The name of the playlist"}, "messages": {"name_too_long": "Playlist names can only be 50 characters long.", "playlist_exists": "A playlist with that name already exists. Please use a different name.", "playlist_created": "Playlist **{name}** has been created."}}, "delete": {"description": "Deletes a playlist", "options": {"playlist": "The playlist you want to delete"}, "messages": {"playlist_not_found": "That playlist doesn't exist.", "playlist_deleted": "Deleted playlist **{playlistName}**."}}, "list": {"description": "Retrieves all playlists for the user", "options": {"user": "The user whose playlists you want to retrieve"}, "messages": {"no_playlists": "This user has no playlists.", "your": "Your", "playlists_title": "{username}'s Playlists", "error": "An error occurred while retrieving the playlists."}}, "load": {"description": "Loads a playlist", "options": {"playlist": "The playlist you want to load"}, "messages": {"playlist_not_exist": "That playlist doesn't exist.", "playlist_empty": "That playlist is empty.", "playlist_loaded": "Loaded `{name}` with `{count}` songs."}}, "removesong": {"description": "Removes a song from the playlist", "options": {"playlist": "The playlist you want to remove from", "song": "The song you want to remove"}, "messages": {"provide_playlist": "Please provide a playlist.", "provide_song": "Please provide a song.", "playlist_not_exist": "That playlist doesn't exist.", "song_not_found": "No matching song found.", "song_removed": "Removed {song} from {playlist}.", "error_occurred": "An error occurred while removing the song."}}, "steal": {"description": "Steals a playlist from another user and adds it to your playlists", "options": {"playlist": "The playlist you want to steal", "user": "The user from whom you want to steal the playlist"}, "messages": {"provide_playlist": "Please provide a playlist name.", "provide_user": "Please mention a user.", "playlist_not_exist": "That playlist doesn't exist for the mentioned user.", "playlist_stolen": "Successfully stole the playlist `{playlist}` from {user}.", "error_occurred": "An error occurred while stealing the playlist."}}}, "buttons": {"invite": "Invite", "support": "Support Server", "previous": "Previous", "resume": "Resume", "stop": "Stop", "skip": "<PERSON><PERSON>", "loop": "Loop", "errors": {"not_author": "You can't use this button."}}, "player": {"errors": {"no_player": "There is no active player in this guild.", "no_channel": "You need to be in a voice channel to use this command.", "queue_empty": "The queue is empty.", "no_previous": "There are no previous songs in the queue.", "no_song": "There are no songs in the queue.", "already_paused": "The song is already paused."}, "trackStart": {"now_playing": "Now Playing", "requested_by": "Requested by {user}", "duration": "Duration", "author": "Author", "not_connected_to_voice_channel": "You are not connected to <#{channel}> to use these buttons.", "need_dj_role": "You need to have the DJ role to use this command.", "previous_by": "Previous by {user}", "no_previous_song": "There is no previous song.", "paused_by": "Paused by {user}", "resumed_by": "Resumed by {user}", "skipped_by": "Skipped by {user}", "no_more_songs_in_queue": "There is no more song in the queue.", "looping_by": "Looping by {user}", "looping_queue_by": "Looping Queue by {user}", "looping_off_by": "Looping Off by {user}"}, "setupStart": {"now_playing": "Now Playing", "description": "[{title}]({uri}) by {author} • `[{length}]` - Requested by <@{requester}>", "error_searching": "There was an error while searching.", "no_results": "There were no results found.", "nothing_playing": "Nothing is playing right now", "queue_too_long": "The queue is too long. The maximum length is {maxQueueSize} songs.", "playlist_too_long": "The playlist is too long. The maximum length is {maxPlaylistSize} songs.", "added_to_queue": "Added [{title}]({uri}) to the queue.", "added_playlist_to_queue": "Added [{length}] songs from the playlist to the queue."}}, "event": {"interaction": {"setup_channel": "You can't use this command in the setup channel.", "no_send_message": "I don't have **`SendMessage`**, **`ViewChannel`**, **`EmbedLinks`** or **`ReadMessageHistory`**  permission.", "no_permission": "I don't have {permissions} permission.", "no_user_permission": "You don't have enough permissions to use this command.", "no_voice_channel": "You must be connected to a voice channel to use this `{command}` command.", "no_connect_permission": "I don't have `CONNECT` permissions to execute this `{command}` command.", "no_speak_permission": "I don't have `SPEAK` permissions to execute this `{command}` command.", "no_request_to_speak": "I don't have `REQUEST TO SPEAK` permission to execute this `{command}` command.", "different_voice_channel": "You are not connected to {channel} to use this `{command}` command.", "no_music_playing": "Nothing is playing right now.", "no_dj_role": "DJ role is not set.", "no_dj_permission": "You need to have the DJ role to use this command.", "cooldown": "Please wait {time} more second(s) before reusing the `{command}` command.", "error": "An error occurred: `{error}`", "vote_button": "Vote for me!", "vote_message": "Wait! You need to vote on top.gg to use this command. Thanks."}, "message": {"prefix_mention": "Hey, my prefix for this server is `{prefix}`. Want more info? then do `{prefix}help`\nStay Safe, Stay Awesome!", "no_send_message": "I don't have **`SendMessage`**, **`ViewChannel`**, **`EmbedLinks`** or **`ReadMessageHistory`**  permission.", "no_permission": "I don't have {permissions} permission.", "no_user_permission": "You don't have enough permissions to use this command.", "no_voice_channel": "You must be connected to a voice channel to use this `{command}` command.", "no_connect_permission": "I don't have `CONNECT` permissions to execute this `{command}` command.", "no_speak_permission": "I don't have `SPEAK` permissions to execute this `{command}` command.", "no_request_to_speak": "I don't have `REQUEST TO SPEAK` permission to execute this `{command}` command.", "different_voice_channel": "You are not connected to {channel} to use this `{command}` command.", "no_music_playing": "Nothing is playing right now.", "no_dj_role": "DJ role is not set.", "no_dj_permission": "You need to have the DJ role to use this command.", "missing_arguments": "Missing Arguments", "missing_arguments_description": "Please provide the required arguments for the `{command}` command.\n\nExamples:\n{examples}", "syntax_footer": "Syntax: [] = optional, <> = required", "cooldown": "Please wait {time} more second(s) before reusing the `{command}` command.", "no_mention_everyone": "You can't use this command with everyone or here. Please use slash command.", "error": "An error occurred: `{error}`", "no_voice_channel_queue": "You are not connected to a voice channel to queue songs.", "no_permission_connect_speak": "I don't have enough permission to connect/speak in <#{channel}>.", "different_voice_channel_queue": "You are not connected to <#{channel}> to queue songs.", "vote_button": "Vote for me!", "vote_message": "Wait! You need to vote on top.gg to use this command. Thanks."}, "setupButton": {"no_voice_channel_button": "You are not connected to a voice channel to use this button.", "different_voice_channel_button": "You are not connected to {channel} to use these buttons.", "now_playing": "Now Playing", "live": "LIVE", "requested_by": "Requested by <@{requester}>", "no_dj_permission": "You need to have the DJ role to use this button.", "volume_set": "Volume set to {vol}%", "volume_footer": "Volume: {vol}%", "paused": "Paused", "resumed": "Resumed", "pause_resume": "{name} the music.", "pause_resume_footer": "{name} by {displayName}", "no_music_to_skip": "There is no music to skip.", "skipped": "Skipped the music.", "skipped_footer": "Skipped by {displayName}", "stopped": "Stopped the music.", "stopped_footer": "Stopped by {displayName}", "nothing_playing": "Nothing is playing right now", "loop_set": "Loop set to {loop}.", "loop_footer": "Loop set to {loop} by {displayName}", "shuffled": "Shuffled the queue.", "no_previous_track": "There is no previous track.", "playing_previous": "Playing the previous track.", "previous_footer": "Playing the previous track by {displayName}", "rewinded": "Rewinded the music.", "rewind_footer": "Rewinded by {displayName}", "forward_limit": "You cannot forward the music more than the length of the song.", "forwarded": "Forwarded the music.", "forward_footer": "Forwarded by {displayName}", "button_not_available": "This button is not available.", "no_music_playing": "There is no music playing in this server."}}, "Evaluate code": "Evaluate code", "Leave a guild": "Leave a guild", "List all guilds the bot is in": "List all guilds the bot is in", "Restart the bot": "Restart the bot", "The loop mode you want to set": "The loop mode you want to set"}