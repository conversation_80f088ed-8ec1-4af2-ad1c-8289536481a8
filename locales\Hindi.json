{"cmd": {"247": {"description": "Bot ko voice channel mein rahne ke liye set karein", "errors": {"not_in_voice": "Is command ka upyog karne ke liye aapko ek voice channel mein hone ki jaroorat hai.", "generic": "Is command ko execute karte samay ek error aaya."}, "messages": {"disabled": "`✅` | 24/7 mode `NIRAKRIYA` kar diya gaya hai", "enabled": "`✅` | 24/7 mode `SAKRIYA` kar diya gaya hai. \n**Bot voice channel chhod nahin dega chahe voice channel mein koi bhi na ho.**"}}, "ping": {"description": "<PERSON>t ka ping dhakeye", "content": "Pinging...", "bot_latency": "Bot V<PERSON>", "api_latency": "API Vilanbta", "requested_by": "{author} dwara an<PERSON><PERSON> kiya gaya"}, "lavalink": {"description": "<PERSON><PERSON><PERSON> a<PERSON>", "title": "Lavalink Aakde", "content": "Player: {players}\nChal rahe Player: {playingPlayers}\nUptime: {uptime}\nCores: {cores} Core(s)\nSmriti Upbhog: {used} / {reservable}\nSystem Load: {systemLoad}%\nLavalink Load: {lavalinkLoad}%"}, "invite": {"description": "Bot nimantran link prapt karein", "content": "Aap niche diye gaye button par click karke mujhe invite kar sakte hain. Koi bug ya outage? Support server mein shamil hon!"}, "help": {"description": "Sahayta menu dhakeye", "options": {"command": "Woh command jiske baare mein aap jankari prapt karna chahte hain"}, "content": "Namaste! Main {bot} hun, [Lavamusic](https://github.com/appujet/lavamusic) aur Discord se bana ek music bot. Kisi command ke baare mein adhik jankari prapt karne ke liye aap `{prefix}help <command>` ka upyog kar sakte hain.", "title": "<PERSON><PERSON><PERSON>", "not_found": "Yeh `{cmdName}` command maujood nahin hai.", "help_cmd": "**Vivaran:** {description}\n**Prayog:** {usage}\n**Udaharan:** {examples}\n**Upnaam:** {aliases}\n**Shreni:** {category}\n**Cooldown:** {cooldown} seconds\n**An<PERSON><PERSON>yan:** {premUser}\n**<PERSON><PERSON>:** {premBot}\n**Keval Developer:** {dev}\n**Slash Command:** {slash}\n**Args:** {args}\n**Player:** {player}\n**DJ:** {dj}\n**DJ <PERSON>:** {djPerm}\n**Voice:** {voice}", "footer": "<PERSON><PERSON> command ke baare mein adhik jankari ke liye {prefix}help <command> ka upyog karein"}, "botinfo": {"description": "<PERSON>t ke baare mein jankari", "content": "<PERSON><PERSON> <PERSON>kari:\n- **Operating System**: {osInfo}\n- **Uptime**: {osUptime}\n- **Hostname**: {osHostname}\n- **CPU Architecture**: {cpuInfo}\n- **CPU Upbhog**: {cpuUsed}%\n- **Smriti Upbhog**: {memUsed}MB / {memTotal}GB\n- **Node Sansकरण**: {nodeVersion}\n- **Discord Sansकरण**: {discordJsVersion}\n- **Se जुड़ा हुआ** {guilds} guilds, {channels} channels, aur {users} users\n- **Kul Commands**: {commands}"}, "about": {"description": "<PERSON>t ke baare mein jankari dhakeye", "fields": {"creator": "<PERSON><PERSON><PERSON>", "repository": "Repository", "support": "<PERSON><PERSON><PERSON>", "description": "Woh sachmuch mein coding anubhav ke liye apna pehla open source project banana chahta tha. Is project mein, use kam bugs ke sath ek project banane ki चुनौती दी गई थी. Aasha hai aap LavaMusic ka upyog karke anand lenge!"}}, "dj": {"description": "DJ mode aur samba<PERSON><PERSON> roles ka prab<PERSON>han karein", "errors": {"provide_role": "<PERSON><PERSON><PERSON> ek role प्रदान करें.", "no_roles": "DJ role k<PERSON> hai.", "invalid_subcommand": "<PERSON><PERSON>ya ek valid subcommand प्रदान करें."}, "messages": {"role_exists": "DJ role <@&{roleId}> pehle se hi juda hua hai.", "role_added": "DJ role <@&{roleId}> juda kar diya gaya hai.", "role_not_found": "DJ role <@&{roleId}> juda nahin hai.", "role_removed": "DJ role <@&{roleId}> हटा diya gaya hai.", "all_roles_cleared": "Saare DJ roles हटा diye gaye hain.", "toggle": "DJ mode ko {status} mein toggle kar diya gaya hai."}, "options": {"add": "Woh DJ role jise aap jodna chahte hain", "remove": "Woh DJ role jise aap hatana chahte hain", "clear": "Saare DJ roles ko clear karta hai", "toggle": "DJ role ko toggle karta hai", "role": "DJ role"}, "subcommands": "Subcommands"}, "language": {"description": "<PERSON><PERSON> ke liye bhasha set karein", "invalid_language": "Kripya ek valid bhasha प्रदान करें. Udaharan: English (United States) ke liye `EnglishUS`\n\nAap supported bhashaon ki सूची [yahan](https://discord.com/developers/docs/reference#locales) dekh sakte hain\n\n**Uplabdh Bhashayen:**\n{languages}", "already_set": "<PERSON><PERSON><PERSON> pehle se hi `{language}` par set hai", "not_set": "<PERSON><PERSON><PERSON> `{language}` par set nahin hai", "set": "`✅` | Bhasha `{language}` par set kar di गई है", "reset": "`✅` | Bhasha ko default bhasha mein reset kar diya gaya hai", "options": {"set": "<PERSON><PERSON> ke liye bhasha set karein", "language": "Woh bhasha jise aap set karna chahte hain", "reset": "<PERSON>hasha ko wapas default bhasha mein badlein"}}, "prefix": {"description": "<PERSON>t ka prefix dhakeye ya set karta hai", "errors": {"prefix_too_long": "Prefix 3 aksharon se adhik lamba nahin ho sakta."}, "messages": {"current_prefix": "Is server ke liye prefix `{prefix}` hai", "prefix_set": "Is server ke liye prefix ab `{prefix}` hai", "prefix_reset": "Is server ke liye prefix ab `{prefix}` hai"}, "options": {"set": "Prefix set karta hai", "prefix": "Woh prefix jise aap set karna chahte hain", "reset": "Prefix ko default par reset karta hai"}}, "setup": {"description": "Bot ko setup karta hai", "errors": {"channel_exists": "Song request channel pehle se hi maujood hai.", "channel_not_exists": "Song request channel maujood nahin hai.", "channel_delete_fail": "Song request channel ko delete kar diya gaya hai. Yadi channel ko normal tarike se delete nahin kiya jata hai, to kripya ise khud delete kar dein."}, "messages": {"channel_created": "Song request channel <#{channelId}> mein bana diya gaya hai.", "channel_deleted": "Song request channel ko delete kar diya gaya hai.", "channel_info": "Song request channel <#{channelId}> hai."}, "options": {"create": "Song request channel banata hai", "delete": "Song request channel ko delete karta hai", "info": "Song request channel dhakeye"}}, "8d": {"description": "8d filter on/off karein", "messages": {"filter_enabled": "`✅` | 8D filter `SAKRIYA` kar diya gaya hai.", "filter_disabled": "`✅` | 8D filter `NIRAKRIYA` kar diya gaya hai."}}, "bassboost": {"description": "bassboost filter on/off karein", "messages": {"filter_enabled": "`✅` | Bassboost filter `SAKRIYA` kar diya gaya hai. \n**<PERSON><PERSON><PERSON><PERSON>, tej awaz mein sunne se aapke kaano ko nuksan pahunch sakta hai!**", "filter_disabled": "`✅` | Bassboost filter `NIRAKRIYA` kar diya gaya hai."}, "options": {"level": "The bassboost level you want to set"}}, "distortion": {"description": "Distorsion filter on/off karein", "messages": {"filter_enabled": "`✅` | Distorsion filter `SAKRIYA` kar diya gaya hai.", "filter_disabled": "`✅` | Distorsion filter `NIRAKRIYA` kar diya gaya hai."}}, "karaoke": {"description": "Karaoke filter on/off karein", "messages": {"filter_enabled": "`✅` | Karaoke filter `SAKRIYA` kar diya gaya hai.", "filter_disabled": "`✅` | Karaoke filter `NIRAKRIYA` kar diya gaya hai."}}, "lowpass": {"description": "Lowpass filter on/off karein", "messages": {"filter_enabled": "`✅` | Lowpass filter `SAKRIYA` kar diya gaya hai.", "filter_disabled": "`✅` | Lowpass filter `NIRAKRIYA` kar diya gaya hai."}}, "nightcore": {"description": "Nightcore filter on/off karein", "messages": {"filter_enabled": "`✅` | Nightcore filter `SAKRIYA` kar diya gaya hai.", "filter_disabled": "`✅` | Nightcore filter `NIRAKRIYA` kar diya gaya hai."}}, "pitch": {"description": "Pitch filter on/off karein", "options": {"pitch": "Woh number jiske liye aap pitch set karna chahte hain (0.5 aur 5 ke beech)"}, "errors": {"invalid_number": "Kripya 0.5 aur 5 ke beech ek valid number प्रदान करें."}, "messages": {"pitch_set": "`✅` | Pitch **{pitch}** par set kar diya gaya hai."}}, "rate": {"description": "Song ki rate badlein", "options": {"rate": "Woh number jiske liye aap rate set karna chahte hain (0.5 aur 5 ke beech)"}, "errors": {"invalid_number": "Kripya 0.5 aur 5 ke beech ek valid number प्रदान करें."}, "messages": {"rate_set": "`✅` | Rate **{rate}** par set kar diya gaya hai."}}, "reset": {"description": "Active filters ko reset karta hai", "messages": {"filters_reset": "`✅` | Filters ko reset kar diya gaya hai."}}, "rotation": {"description": "Rotation filter on/off karein", "messages": {"enabled": "`✅` | Rotation filter `SAKRIYA` kar diya gaya hai.", "disabled": "`✅` | Rotation filter `NIRAKRIYA` kar diya gaya hai."}}, "speed": {"description": "Song ki speed badlein", "options": {"speed": "Woh speed jise aap set karna chahte hain"}, "messages": {"invalid_number": "Kripya 0.5 aur 5 ke beech ek valid number प्रदान करें.", "set_speed": "`✅` | Speed **{speed}** par set kar diya gaya hai."}}, "tremolo": {"description": "Tremolo filter on/off karein", "messages": {"enabled": "`✅` | Tremolo filter `SAKRIYA` kar diya gaya hai.", "disabled": "`✅` | Tremolo filter `NIRAKRIYA` kar diya gaya hai."}}, "vibrato": {"description": "Vibrato filter on/off karein", "messages": {"enabled": "`✅` | Vibrato filter `SAKRIYA` kar diya gaya hai.", "disabled": "`✅` | Vibrato filter `NIRAKRIYA` kar diya gaya hai."}}, "autoplay": {"description": "Autoplay ko toggle karta hai", "messages": {"enabled": "`✅` | Autoplay `SAKRIYA` kar diya gaya hai.", "disabled": "`✅` | Autoplay `NIRAKRIYA` kar diya gaya hai."}}, "clearqueue": {"description": "Queue ko clear karta hai", "messages": {"cleared": "<PERSON>ue ko clear kar diya gaya hai."}}, "grab": {"description": "Aapke DM par chal rahe song ko grab karta hai", "content": "**Aawadhi:** {length}\n**Anurodh karta:** <@{requester}>\n**Link:** [<PERSON><PERSON> click karein]({uri})", "check_dm": "Kripya apna DM check karein.", "dm_failed": "Main aapko DM nahin bhej saka."}, "join": {"description": "Voice channel mein shamil hota hai", "already_connected": "Main pehle se hi <#{channelId}> se juda hua hun.", "no_voice_channel": "Is command ka upyog karne ke liye aapko ek voice channel mein hone ki jaroorat hai.", "joined": "<#{channelId}> mein safa<PERSON>vak shamil hua."}, "leave": {"description": "Voice channel chhod deta hai", "left": "<#{channelId}> safaltapoorvak chhod diya.", "not_in_channel": "Main kisi voice channel mein nahin hun."}, "loop": {"description": "Mojuda song ya queue ko loop karein", "looping_song": "**Song ko loop kar raha hun.**", "looping_queue": "**<PERSON>ue ko loop kar रहा हूँ.**", "looping_off": "**Looping ab band hai.**"}, "nowplaying": {"description": "<PERSON><PERSON><PERSON> chal rahe song ko dhakeye", "now_playing": "<PERSON><PERSON><PERSON>", "track_info": "[{title}]({uri}) - Anurodh karta: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "Mojuda song ko pause karein", "successfully_paused": "Song ko safa<PERSON><PERSON>or<PERSON>k pause kar diya गया."}, "play": {"description": "YouTube, Spotify ya http se ek song play karein", "options": {"song": "Woh song jise aap play karna chahte hain"}, "loading": "Loading...", "errors": {"search_error": "<PERSON><PERSON><PERSON>te samay ek error aaya.", "no_results": "Koi result nahin mila.", "queue_too_long": "<PERSON>ue bahut lambi hai. <PERSON>hi<PERSON><PERSON> lambai {maxQueueSize} songs hai.", "playlist_too_long": "Playlist bahut lambi hai. <PERSON><PERSON><PERSON><PERSON> lambai {maxPlaylistSize} songs hai."}, "added_to_queue": "[{title}]({uri}) ko queue mein jod diya gaya है.", "added_playlist_to_queue": "{length} songs ko queue mein jod diya गया है."}, "playnext": {"description": "<PERSON>ue mein aage play karne ke liye song jodein", "options": {"song": "Woh song jise aap play karna chahte hain"}, "loading": "Loading...", "errors": {"search_error": "<PERSON><PERSON><PERSON>te samay ek error aaya.", "no_results": "Koi result nahin mila.", "queue_too_long": "<PERSON>ue bahut lambi hai. <PERSON>hi<PERSON><PERSON> lambai {maxQueueSize} songs hai.", "playlist_too_long": "Playlist bahut lambi hai. <PERSON><PERSON><PERSON><PERSON> lambai {maxPlaylistSize} songs hai."}, "added_to_play_next": "[{title}]({uri}) ko queue mein aage play karne ke liye jod diya गया है.", "added_playlist_to_play_next": "{length} songs ko queue mein aage play karne ke liye jod diya गया है."}, "queue": {"description": "<PERSON><PERSON>da queue dhakeye", "now_playing": "<PERSON><PERSON><PERSON> chal raha hai: [{title}]({uri}) - <PERSON><PERSON><PERSON> karta: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "live": "LIVE", "track_info": "{index}. [{title}]({uri}) - Anurodh karta: <@{requester}> - Aawadhi: `{duration}`", "title": "Queue", "page_info": "{total} mein se page {index}"}, "remove": {"description": "Queue se ek song hatayein", "options": {"song": "Woh song number jise aap hatana chahte hain"}, "errors": {"no_songs": "Queue mein koi song nahin hai.", "invalid_number": "<PERSON><PERSON><PERSON> ek valid song number प्रदान करें."}, "messages": {"removed": "Song number {songNumber} ko queue se हटा diya gaya है."}}, "replay": {"description": "Mojuda track ko replay karein", "errors": {"not_seekable": "Is track ko replay nahin kiya ja sakta kyonki yeh seekable nahin hai."}, "messages": {"replaying": "<PERSON><PERSON><PERSON> track ko replay कर रहा हूँ."}}, "resume": {"description": "Mojuda song ko <PERSON> karein", "errors": {"not_paused": "Player pause nahin hai."}, "messages": {"resumed": "Player ko resume kar diya gaya है."}}, "search": {"description": "<PERSON><PERSON> song khojein", "options": {"song": "Woh song jise aap khोजना chahte hain"}, "errors": {"no_results": "Koi result nahin mila.", "search_error": "<PERSON><PERSON><PERSON>te samay ek error aaya."}, "messages": {"added_to_queue": "[{title}]({uri}) ko queue mein jod diya gaya है."}}, "seek": {"description": "Song mein ek nishchit samay tak khojein", "options": {"duration": "Woh aawadhi jiske liye aap khोजना chahte hain"}, "errors": {"invalid_format": "Invalid samay format. Udaharan: seek 1m, seek 1h 30m", "not_seekable": "<PERSON>h track seekable nahin hai.", "beyond_duration": "{length} ki song aawadhi ke baad khोजना संभव नहीं है."}, "messages": {"seeked_to": "{duration} tak khoja गया"}}, "shuffle": {"description": "<PERSON>ue ko shuffle karein", "messages": {"shuffled": "Queue ko shuffle kar diya गया है."}}, "skip": {"description": "Mojuda song ko skip karein", "messages": {"skipped": "[{title}]({uri}) ko skip kar diya गया है."}}, "skipto": {"description": "Queue mein ek specific song tak skip karein", "options": {"number": "Woh song number jiske liye aap skip karna chahte hain"}, "errors": {"invalid_number": "Kripya ek valid number प्रदान करें."}, "messages": {"skipped_to": "Song number {number} tak skip कर दिया गया है."}}, "stop": {"description": "Music band karein aur queue clear karein", "messages": {"stopped": "Music band kar diya गया है aur queue clear कर दी गई है."}}, "volume": {"description": "Player ka volume set karein", "options": {"number": "Woh volume jise aap set karna chahte hain"}, "messages": {"invalid_number": "Kripya ek valid number प्रदान करें.", "too_low": "Volume 0 se kam nahin ho sakta.", "too_high": "Volume 200 se adhik nahin ho sakta. Kya aap apne kaano ya speakers ko nuksan pahunchana chahte hain? Hmmm, mujhe nahin lagta ki yeh ek achcha vichar hai.", "set": "Volume {volume} par set कर दिया गया है"}}, "addsong": {"description": "Playlist mein ek song jodein", "options": {"playlist": "Woh playlist ji<PERSON><PERSON> aap jodna chahte hain", "song": "Woh song jise aap jodna chahte hain"}, "messages": {"no_playlist": "<PERSON><PERSON><PERSON> ek playlist प्र<PERSON><PERSON><PERSON> करें", "no_song": "<PERSON><PERSON><PERSON> ek song प्रदान करें", "playlist_not_found": "Woh playlist ma<PERSON><PERSON><PERSON> nahin hai", "no_songs_found": "Koi song nahin mila", "added": "{playlist} mein {count} song(s) jod diye gaye hain"}}, "create": {"description": "Ek playlist <PERSON><PERSON><PERSON>", "options": {"name": "Playlist ka naam"}, "messages": {"name_too_long": "Playlist ke naam sirf 50 aksharon tak ke ho sakte hain.", "playlist_exists": "Us naam ki ek playlist pehle se hi maujood hai. Kripya ek alag naam ka upyog karein.", "playlist_created": "Playlist **{name}** bana di गई है."}}, "delete": {"description": "Ek playlist ko delete karein", "options": {"playlist": "Woh playlist jise aap delete karna chahte hain"}, "messages": {"playlist_not_found": "<PERSON><PERSON> playlist ma<PERSON><PERSON><PERSON> <PERSON>hin hai.", "playlist_deleted": "Playlist **{playlistName}** ko delete kar diya गया है."}}, "list": {"description": "User ke liye sabhi playlists ko prapt karta hai", "options": {"user": "Woh user jiske playlists aap prapt karna chahte hain"}, "messages": {"no_playlists": "Is user ke paas koi playlist nahin hai.", "your": "Aapka", "playlists_title": "{username} ki Playlists", "error": "Playlists ko prapt karte samay ek error aaya."}}, "load": {"description": "Ek playlist ko <PERSON> karein", "options": {"playlist": "Woh playlist ji<PERSON> aap load karna chahte hain"}, "messages": {"playlist_not_exist": "<PERSON><PERSON> playlist ma<PERSON><PERSON><PERSON> <PERSON>hin hai.", "playlist_empty": "<PERSON><PERSON> playlist k<PERSON> hai.", "playlist_loaded": "{count} songs ke sath `{name}` load kar di गई है."}}, "removesong": {"description": "Playlist se ek song hatayein", "options": {"playlist": "Woh playlist jisse aap hatana chahte hain", "song": "Woh song jise aap hatana chahte hain"}, "messages": {"provide_playlist": "<PERSON><PERSON><PERSON> ek playlist प्र<PERSON><PERSON><PERSON> करें.", "provide_song": "<PERSON><PERSON><PERSON> ek song प्रदान करें.", "playlist_not_exist": "<PERSON><PERSON> playlist ma<PERSON><PERSON><PERSON> <PERSON>hin hai.", "song_not_found": "<PERSON><PERSON> matching song nahin mila.", "song_removed": "{playlist} se {song} ko हटा diya गया है.", "error_occurred": "Song ko हटाते समय एक error aaya."}}, "steal": {"description": "<PERSON><PERSON> doosre user se ek playlist chura lete hain aur use aapki playlists mein jod dete hain", "options": {"playlist": "Woh playlist ji<PERSON> aap churana chahte hain", "user": "Woh user jisse aap playlist churana chahte hain"}, "messages": {"provide_playlist": "<PERSON><PERSON><PERSON> ek playlist naam प्रदा<PERSON> करें.", "provide_user": "Kripya ek user ka ullekh karein.", "playlist_not_exist": "Woh playlist mentioned user ke liye maujood nahin hai.", "playlist_stolen": "{user} se playlist `{playlist}` ko safaltapoorvak chura लिया गया है.", "error_occurred": "Playlist ko churaate समय एक error aaya."}}, "lyrics": {"description": "Get's the lyrics of the currently playing track"}, "fairplay": {"description": "Set the bot to play music fairly"}, "playlocal": {"description": "Plays an your local audio files", "options": {"file": "The audio file you want to play"}}, "say": {"description": "Quick text to speech conversion", "options": {"text": "The text to say"}}, "speak": {"description": "Convert text to speech with advanced voice options", "options": {"text": "The text to convert to speech", "voice": "The voice to use for speech synthesis", "speed": "The speed of speech (0.5 to 2.0)"}}, "mluvit": {"description": "Czech text to speech conversion", "options": {"text": "Text to convert to Czech speech", "voice": "Czech voice to use (<PERSON><PERSON><PERSON> or <PERSON><PERSON>)", "speed": "Speech speed (0.5 to 2.0)"}}}, "buttons": {"invite": "Niman<PERSON><PERSON>", "support": "Support Server", "previous": "<PERSON><PERSON><PERSON><PERSON>", "resume": "Resume", "stop": "<PERSON><PERSON>", "skip": "<PERSON><PERSON>", "loop": "Loop", "errors": {"not_author": "Aap is button ka upyog nahin kar sakte."}}, "player": {"errors": {"no_player": "Is guild mein koi active player nahin hai.", "no_channel": "Is command ka upyog karne ke liye aapko ek voice channel mein hone ki jaroorat hai.", "queue_empty": "<PERSON>ue khali hai.", "no_previous": "<PERSON>ue mein koi pichhla song nahin hai.", "no_song": "Queue mein koi song nahin hai.", "already_paused": "Song pehle se hi pause hai."}, "trackStart": {"now_playing": "<PERSON><PERSON><PERSON>", "requested_by": "{user} dwara an<PERSON>dh kiya gaya", "duration": "<PERSON><PERSON><PERSON>", "author": "Lekhak", "not_connected_to_voice_channel": "In buttons ka upyog karne ke liye aap <#{channel}> se nahin जुड़े हैं.", "need_dj_role": "Is command ka upyog karne ke liye aapke paas DJ role hone ki jaroorat hai.", "previous_by": "{user} dwara pichhla", "no_previous_song": "<PERSON>i p<PERSON>hla song nahin hai.", "paused_by": "{user} dwara pause kiya gaya", "resumed_by": "{user} dwara resume kiya gaya", "skipped_by": "{user} dwara skip kiya गया", "no_more_songs_in_queue": "Queue mein koi aur song nahin hai.", "looping_by": "{user} dwara looping", "looping_queue_by": "{user} dwara queue looping", "looping_off_by": "{user} dwara looping band"}, "setupStart": {"now_playing": "<PERSON><PERSON><PERSON>", "description": "{author} dwara [{title}]({uri}) • `[{length}]` - <@{requester}> dwara an<PERSON>dh kiya gaya", "error_searching": "<PERSON><PERSON><PERSON>te samay ek error aaya.", "no_results": "Koi result nahin mila.", "nothing_playing": "<PERSON><PERSON><PERSON> k<PERSON>h bhi play nahin ho raha hai.", "queue_too_long": "<PERSON>ue bahut lambi hai. <PERSON>hi<PERSON><PERSON> lambai {maxQueueSize} songs hai.", "playlist_too_long": "Playlist bahut lambi hai. <PERSON><PERSON><PERSON><PERSON> lambai {maxPlaylistSize} songs hai.", "added_to_queue": "[{title}]({uri}) ko queue mein jod diya gaya है.", "added_playlist_to_queue": "Playlist se [{length}] songs ko queue mein jod diya gaya है."}}, "event": {"interaction": {"setup_channel": "Aap setup channel mein is command ka upyog nahin kar sakte.", "no_send_message": "<PERSON><PERSON><PERSON> `{guild}` mein **`SendMessage`** permission nahin hai\nchannel: {channel}.", "no_embed_links": "<PERSON><PERSON>he **`EmbedLinks`** permission nahin hai.", "no_permission": "Is command ko execute karne ke liye mere paas पर्याप्त permissions nahin hain.", "no_user_permission": "Is command ka upyog karne ke liye aapke paas पर्याप्त permissions nahin hain.", "no_voice_channel": "Is `{command}` command ka upyog karne ke liye aapko ek voice channel mein जुड़ा होना होगा.", "no_connect_permission": "Is `{command}` command ko execute karne ke liye mere paas `CONNECT` permissions nahin hain.", "no_speak_permission": "Is `{command}` command ko execute karne ke liye mere paas `SPEAK` permissions nahin hain.", "no_request_to_speak": "Is `{command}` command ko execute karne ke liye mere paas `REQUEST TO SPEAK` permission nahin hai.", "different_voice_channel": "Is `{command}` command ka upyog karne ke liye aap {channel} se nahin जुड़े हैं.", "no_music_playing": "<PERSON><PERSON><PERSON> k<PERSON>h bhi play nahin ho raha hai.", "no_dj_role": "DJ role set nahin hai.", "no_dj_permission": "Is command ka upyog karne ke liye aapke paas DJ role hone ki jaroorat hai.", "cooldown": "`{command}` command ka fir se upyog karne se pehle kripya {time} second(s) aur प्रतीक्षा करें.", "error": "Ek error aaya: `{error}`"}, "message": {"prefix_mention": "Hey, is server ke liye mera prefix `{prefix}` hai. <PERSON>r jan<PERSON> chahiye? To `{prefix}help` ka<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>!", "no_send_message": "<PERSON><PERSON><PERSON> `{guild}` mein **`SendMessage`** permission nahin hai\nchannel: {channel}.", "no_embed_links": "<PERSON><PERSON>he **`EmbedLinks`** permission nahin hai.", "no_permission": "Is command ko execute karne ke liye mere paas पर्याप्त permissions nahin hain.", "no_user_permission": "Is command ka upyog karne ke liye aapke paas पर्याप्त permissions nahin hain.", "no_voice_channel": "Is `{command}` command ka upyog karne ke liye aapko ek voice channel mein जुड़ा होना होगा.", "no_connect_permission": "Is `{command}` command ko execute karne ke liye mere paas `CONNECT` permissions nahin hain.", "no_speak_permission": "Is `{command}` command ko execute karne ke liye mere paas `SPEAK` permissions nahin hain.", "no_request_to_speak": "Is `{command}` command ko execute karne ke liye mere paas `REQUEST TO SPEAK` permission nahin hai.", "different_voice_channel": "Is `{command}` command ka upyog karne ke liye aap {channel} se nahin जुड़े हैं.", "no_music_playing": "<PERSON><PERSON><PERSON> k<PERSON>h bhi play nahin ho raha hai.", "no_dj_role": "DJ role set nahin hai.", "no_dj_permission": "Is command ka upyog karne ke liye aapke paas DJ role hone ki jaroorat hai.", "missing_arguments": "Arguments laapata hain", "missing_arguments_description": "<PERSON><PERSON><PERSON> `{command}` command ke liye jaroori arguments प्रदान करें.\n\nUdaharan:\n{examples}", "syntax_footer": "Syntax: [] = optional, <> = jaroori", "cooldown": "`{command}` command ka fir se upyog karne se pehle kripya {time} second(s) aur प्रतीक्षा करें.", "no_mention_everyone": "Aap is command ka upyog sabhi ke sath ya yahan nahin kar sakte.", "error": "Ek error aaya: `{error}`", "no_voice_channel_queue": "Songs ko queue mein jodne ke liye aap kisi voice channel से जुड़े नहीं हैं.", "no_permission_connect_speak": "Mere paas <#{channel}> mein connect/speak karne ki पर्याप्त permission nahin hai.", "different_voice_channel_queue": "Songs ko queue mein jodne ke liye aap <#{channel}> se nahin जुड़े हैं."}, "setupButton": {"no_voice_channel_button": "Is button ka upyog karne ke liye aap kisi voice channel से जुड़े नहीं हैं.", "different_voice_channel_button": "In buttons ka upyog karne ke liye aap {channel} se nahin जुड़े हैं.", "now_playing": "<PERSON><PERSON><PERSON>", "live": "LIVE", "requested_by": "<@{requester}> dwara anurodh kiya गया", "no_dj_permission": "Is command ka upyog karne ke liye aapke paas DJ role hone ki jaroorat hai.", "volume_set": "Volume {vol}% par set कर दिया गया है", "volume_footer": "Volume: {vol}%", "paused": "Pause", "resumed": "Resume", "pause_resume": "Music ko {name} karein.", "pause_resume_footer": "{displayName} dwara {name} kiya गया", "no_music_to_skip": "<PERSON><PERSON> karne ke liye koi music nahin hai.", "skipped": "Music ko skip kar diya गया है.", "skipped_footer": "{displayName} dwara skip kiya गया", "stopped": "Music ko band कर diya गया है.", "stopped_footer": "{displayName} dwara band kiya गया", "nothing_playing": "<PERSON><PERSON><PERSON> k<PERSON>h bhi play nahin ho raha hai", "loop_set": "Loop {loop} par set कर दिया गया है.", "loop_footer": "{displayName} dwara loop {loop} par set kiya गया", "shuffled": "<PERSON>ue ko shuffle कर diya गया है.", "no_previous_track": "<PERSON><PERSON> p<PERSON>a track nahin hai.", "playing_previous": "Pichhla track play कर रहा हूँ.", "previous_footer": "{displayName} dwara pichhla track play kiya जा रहा है", "rewinded": "Music ko rewind कर diya गया है.", "rewind_footer": "{displayName} dwara rewind kiya गया", "forward_limit": "Aap music ko song ki lambai se zyada forward nahin kar sakte.", "forwarded": "Music ko forward कर diya गया है.", "forward_footer": "{displayName} dwara forward kiya गया", "button_not_available": "Yeh button uplabdh nahin hai.", "no_music_playing": "<PERSON><PERSON><PERSON> k<PERSON>h bhi play nahin ho raha hai."}}, "Evaluate code": "Evaluate code", "Leave a guild": "Leave a guild", "List all guilds the bot is in": "List all guilds the bot is in", "Restart the bot": "Restart the bot", "The loop mode you want to set": "The loop mode you want to set"}