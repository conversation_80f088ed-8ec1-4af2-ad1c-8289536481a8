{"cmd": {"247": {"description": "Заставляет бота оставаться в голосовом канале", "errors": {"not_in_voice": "Вам нужно быть в голосовом канале, чтобы использовать эту команду.", "generic": "Произошла ошибка при попытке выполнить эту команду."}, "messages": {"disabled": "`✅` | Режим 24/7 был `ОТКЛЮЧЕН`", "enabled": "`✅` | Режим 24/7 был `ВКЛЮЧЕН`. \n**Бот не покинет голосовой канал, даже если там никого не будет.**"}}, "ping": {"description": "Показывает пинг бота.", "content": "Пингую...", "bot_latency": "Задержка бота", "api_latency": "Задержка API", "requested_by": "За<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {author}"}, "lavalink": {"description": "Показывает текущую статистику Lavalink.", "title": "Статистика Lavalink", "content": "Игроки: {players}\nИграющие игроки: {playingPlayers}\nВремя работы: {uptime}\nЯдра: {cores} Ядро(я)\nИспользование памяти: {used} / {reservable}\nНагрузка системы: {systemLoad}%\nНагрузка Lavalink: {lavalinkLoad}%"}, "invite": {"description": "Получите ссылку для приглашения бота.", "content": "Вы можете пригласить меня, нажав кнопку ниже.  Любые ошибки или перебои в работе?  Присоединяйтесь к серверу поддержки!"}, "help": {"description": "Показывает меню справки.", "options": {"command": "Команда, о которой вы хотите получить информацию"}, "content": "Привет!  Я {bot}, музыкальный бот, созданный с помощью [Lavamusic](https://github.com/appujet/lavamusic) и Discord.  Вы можете использовать `{prefix}help <command>`, чтобы получить больше информации о команде.", "title": "Меню справки", "not_found": "Команда `{cmdName}` не существует.", "help_cmd": "**Описание:** {description}\n**Использование:** {usage}\n**Примеры:** {examples}\n**Псевдонимы:** {aliases}\n**Категория:** {category}\n**Охлаждение:** {cooldown} секунд\n**Разрешения:** {premUser}\n**Разрешения бота:** {premBot}\n**Только для разработчиков:** {dev}\n**Команда Slash:** {slash}\n**Args:** {args}\n**Проигрыватель:** {player}\n**DJ:** {dj}\n**Разрешения DJ:** {djPerm}\n**Голос:** {voice}", "footer": "Используйте {prefix}help <command>, чтобы получить больше информации о команде"}, "botinfo": {"description": "Информация о боте", "content": "Информация о боте:\n- **Операционная система**: {osInfo}\n- **Время работы**: {osUptime}\n- **Имя хоста**: {osHostname}\n- **Архитектура ЦП**: {cpuInfo}\n- **Использование ЦП**: {cpuUsed}%\n- **Использование памяти**: {memUsed}MB / {memTotal}GB\n- **Версия Node**: {nodeVersion}\n- **Версия Discord**: {discordJsVersion}\n- **Подключен к** {guilds} серверам, {channels} каналам и {users} пользователям\n- **Общее количество команд**: {commands}"}, "about": {"description": "Показывает информацию о боте", "fields": {"creator": "Создатель", "repository": "Репозиторий", "support": "Поддержка", "description": "Он очень хотел создать свой первый проект с открытым исходным кодом, чтобы получить больше опыта в программировании.  В этом проекте он был поставлен перед задачей создания проекта с меньшим количеством ошибок.  Надеюсь, вам понравится использовать LavaMusic!"}}, "dj": {"description": "Управление режимом DJ и связанными ролями", "errors": {"provide_role": "Пожалуйста, укажите роль.", "no_roles": "<PERSON><PERSON><PERSON>ь <PERSON> пуста.", "invalid_subcommand": "Пожалуйста, укажите допустимую подкоманду."}, "messages": {"role_exists": "<PERSON>оль <PERSON> <@&{roleId}> уже добавлена.", "role_added": "<PERSON>оль <PERSON> <@&{roleId}> была добавлена.", "role_not_found": "<PERSON>оль <PERSON> <@&{roleId}> не добавлена.", "role_removed": "<PERSON>оль <PERSON> <@&{roleId}> была удалена.", "all_roles_cleared": "Все роли DJ были удалены.", "toggle": "<PERSON>ежим DJ был переключен на {status}."}, "options": {"add": "<PERSON>оль <PERSON>, которую вы хотите добавить", "remove": "<PERSON>оль <PERSON>, которую вы хотите удалить", "clear": "Очищает все роли DJ", "toggle": "Включает/отключает роль DJ", "role": "<PERSON>оль <PERSON>"}, "subcommands": "Подкоманды"}, "language": {"description": "Установить язык для бота", "invalid_language": "Пожалуйста, укажите допустимый язык.  Пример: `EnglishUS` для английского (США)\n\nВы можете найти список поддерживаемых языков [здесь](https://discord.com/developers/docs/reference#locales)\n\n**Доступные языки:**\n{languages}", "already_set": "Язык уже установлен на `{language}`", "not_set": "Язык не установлен.", "set": "`✅` | Язык был установлен на `{language}`", "reset": "`✅` | Язык был сброшен на язык по умолчанию", "options": {"set": "Установить язык для бота", "language": "Язык, который вы хотите установить", "reset": "Изменить язык обратно на язык по умолчанию"}}, "prefix": {"description": "Показывает или устанавливает префикс бота", "errors": {"prefix_too_long": "Префикс не может быть длиннее 3 символов."}, "messages": {"current_prefix": "Префикс для этого сервера: `{prefix}`", "prefix_set": "Префикс для этого сервера теперь `{prefix}`", "prefix_reset": "Префикс для этого сервера теперь `{prefix}`"}, "options": {"set": "Устанавливает префикс", "prefix": "Префикс, который вы хотите установить", "reset": "Сбрасывает префикс к прежнему значению"}}, "setup": {"description": "Настройка бота", "errors": {"channel_exists": "Канал запросов песен уже существует.", "channel_not_exists": "Канала запросов песен не существует.", "channel_delete_fail": "Канал запросов песен был удален.  Если канал не удаляется нормально, удалите его самостоятельно."}, "messages": {"channel_created": "Канал запросов песен был создан в <#{channelId}>.", "channel_deleted": "Канал запросов песен был удален.", "channel_info": "Канал запросов песен - <#{channelId}>."}, "options": {"create": "Создает канал запросов песен", "delete": "Удаляет канал запросов песен", "info": "Показывает канал запросов песен"}}, "8d": {"description": "вкл/выкл фильтр 8d", "messages": {"filter_enabled": "`✅` | Фильтр 8D был `ВКЛЮЧЕН`.", "filter_disabled": "`✅` | Фильтр 8D был `ОТКЛЮЧЕН`."}}, "bassboost": {"description": "вкл/выкл фильтр усиления басов", "messages": {"filter_enabled": "`✅` | Фильтр усиления басов был `ВКЛЮЧЕН`. \n**Будьте осторожны, слишком громкое прослушивание может повредить слух!**", "filter_disabled": "`✅` | Фильтр усиления басов был `ОТКЛЮЧЕН`."}, "options": {"level": "The bassboost level you want to set"}}, "distortion": {"description": "Включает/отключает фильтр искажения", "messages": {"filter_enabled": "`✅` | Фильтр искажения был `ВКЛЮЧЕН`.", "filter_disabled": "`✅` | Фильтр искажения был `ОТКЛЮЧЕН`."}}, "karaoke": {"description": "Включает/отключает фильтр караоке", "messages": {"filter_enabled": "`✅` | Фильтр караоке был `ВКЛЮЧЕН`.", "filter_disabled": "`✅` | Фильтр караоке был `ОТКЛЮЧЕН`."}}, "lowpass": {"description": "Включает/отключает фильтр нижних частот", "messages": {"filter_enabled": "`✅` | Фильтр нижних частот был `ВКЛЮЧЕН`.", "filter_disabled": "`✅` | Фильтр нижних частот был `ОТКЛЮЧЕН`."}}, "nightcore": {"description": "Включает/отключает фильтр nightcore", "messages": {"filter_enabled": "`✅` | Фильтр nightcore был `ВКЛЮЧЕН`.", "filter_disabled": "`✅` | Фильтр nightcore был `ОТКЛЮЧЕН`."}}, "pitch": {"description": "Включает/отключает фильтр тона", "options": {"pitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>, на которое вы хотите установить тон (от 0,5 до 5)"}, "errors": {"invalid_number": "Пожалуйста, введите допустимое число от 0,5 до 5."}, "messages": {"pitch_set": "`✅` | Тон был установлен на **{pitch}**."}}, "rate": {"description": "Изменяет темп песни", "options": {"rate": "<PERSON>и<PERSON><PERSON><PERSON>, на которое вы хотите установить темп (от 0,5 до 5)"}, "errors": {"invalid_number": "Пожалуйста, введите допустимое число от 0,5 до 5."}, "messages": {"rate_set": "`✅` | Темп был установлен на **{rate}**."}}, "reset": {"description": "Сбрасывает активные фильтры", "messages": {"filters_reset": "`✅` | Фильтры были сброшены."}}, "rotation": {"description": "Включает/отключает фильтр вращения", "messages": {"enabled": "`✅` | Фильтр вращения был `ВКЛЮЧЕН`.", "disabled": "`✅` | Фильтр вращения был `ОТКЛЮЧЕН`."}}, "speed": {"description": "Изменяет скорость песни", "options": {"speed": "Скорость, которую вы хотите установить"}, "messages": {"invalid_number": "Пожалуйста, введите допустимое число от 0,5 до 5.", "set_speed": "`✅` | Скорость была установлена на **{speed}**."}}, "tremolo": {"description": "Включает/отключает фильтр тремоло", "messages": {"enabled": "`✅` | Фильтр тремоло был `ВКЛЮЧЕН`.", "disabled": "`✅` | Фильтр тремоло был `ОТКЛЮЧЕН`."}}, "vibrato": {"description": "Включает/отключает фильтр вибрато", "messages": {"enabled": "`✅` | Фильтр вибрато был `ВКЛЮЧЕН`.", "disabled": "`✅` | Фильтр вибрато был `ОТКЛЮЧЕН`."}}, "autoplay": {"description": "Включает/отключает автовоспроизведение", "messages": {"enabled": "`✅` | Автовоспроизведение было `ВКЛЮЧЕНО`.", "disabled": "`✅` | Автовоспроизведение было `ОТКЛЮЧЕНО`."}}, "clearqueue": {"description": "Очищает очередь", "messages": {"cleared": "Очередь была очищена."}}, "grab": {"description": "Забирает текущую воспроизводимую песню в ваш DM", "content": "**Длительность:** {length}\n**Запрошено:** <@{requester}>\n**Ссылка:** [Нажмите сюда]({uri})", "check_dm": "Проверьте свой DM.", "dm_failed": "Я не смог отправить вам DM."}, "join": {"description": "Присоединяется к голосовому каналу", "already_connected": "Я уже подключен к <#{channelId}>.", "no_voice_channel": "Вам нужно быть в голосовом канале, чтобы использовать эту команду.", "joined": "Успешно подключен к <#{channelId}>."}, "leave": {"description": "Покидает голосовой канал", "left": "Успешно покинул <#{channelId}>.", "not_in_channel": "Я не в голосовом канале."}, "loop": {"description": "Зацикливает текущую песню или очередь", "looping_song": "**Зацикливание песни.**", "looping_queue": "**Зацикливание очереди.**", "looping_off": "**Зацикливание теперь выключено.**"}, "nowplaying": {"description": "Показывает текущую воспроизводимую песню", "now_playing": "Сейчас играет", "track_info": "[{title}]({uri}) - Запрошено: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "Ставит на паузу текущую песню", "successfully_paused": "Песня успешно поставлена на паузу."}, "play": {"description": "Проигрывает песню из YouTube, Spotify или http", "options": {"song": "Песня, которую вы хотите воспроизвести"}, "loading": "Загрузка...", "errors": {"search_error": "Произошла ошибка во время поиска.", "no_results": "Результаты не найдены.", "queue_too_long": "Очередь слишком длинная.  Максимальная длина - {maxQueueSize} песен.", "playlist_too_long": "Список воспроизведения слишком длинный.  Максимальная длина - {maxPlaylistSize} песен."}, "added_to_queue": "Добавлена [{title}]({uri}) в очередь.", "added_playlist_to_queue": "Добавлено {length} песен в очередь."}, "playnext": {"description": "Добавить песню для воспроизведения следующей в очереди", "options": {"song": "Песня, которую вы хотите воспроизвести"}, "loading": "Загрузка...", "errors": {"search_error": "Произошла ошибка во время поиска.", "no_results": "Результаты не найдены.", "queue_too_long": "Очередь слишком длинная.  Максимальная длина - {maxQueueSize} песен.", "playlist_too_long": "Список воспроизведения слишком длинный.  Максимальная длина - {maxPlaylistSize} песен."}, "added_to_play_next": "Добавлено [{title}]({uri}) для воспроизведения следующей в очереди.", "added_playlist_to_play_next": "Добавлено {length} песен для воспроизведения следующими в очереди."}, "queue": {"description": "Показывает текущую очередь", "now_playing": "Сейчас играет: [{title}]({uri}) - Запрошено: <@{requester}> - Длительность: `{duration}`", "live": "В ПРЯМОМ ЭФИРЕ", "track_info": "{index}. [{title}]({uri}) - Запрошено: <@{requester}> - Длительность: `{duration}`", "title": "Очередь", "page_info": "Страница {index} из {total}"}, "remove": {"description": "Удаляет песню из очереди", "options": {"song": "Номер песни, которую вы хотите удалить"}, "errors": {"no_songs": "В очереди нет песен.", "invalid_number": "Пожалуйста, введите допустимый номер песни."}, "messages": {"removed": "Удалена песня номер {songNumber} из очереди."}}, "replay": {"description": "Повторно проигрывает текущую дорожку", "errors": {"not_seekable": "Невозможно повторить эту дорожку, так как она не может быть перемотана."}, "messages": {"replaying": "Повторно проигрывается текущая дорожка."}}, "resume": {"description": "Возобновляет текущую песню", "errors": {"not_paused": "Проигрыватель не на паузе."}, "messages": {"resumed": "Проигрыватель возобновлен."}}, "search": {"description": "Ищет песню", "options": {"song": "Песня, которую вы хотите найти"}, "errors": {"no_results": "Результаты не найдены.", "search_error": "Произошла ошибка во время поиска."}, "messages": {"added_to_queue": "Добавлена [{title}]({uri}) в очередь."}}, "seek": {"description": "Перемещает к определенному времени в песне", "options": {"duration": "Длительность, к которой вы хотите переместиться"}, "errors": {"invalid_format": "Неверный формат времени.  Примеры: seek 1m, seek 1h 30m", "not_seekable": "Эта дорожка не может быть перемотана.", "beyond_duration": "Невозможно переместиться за пределы длительности песни {length}."}, "messages": {"seeked_to": "Перемещено к {duration}"}}, "shuffle": {"description": "Перемешивает очередь", "messages": {"shuffled": "Очередь была перемешана."}}, "skip": {"description": "Пропускает текущую песню", "messages": {"skipped": "Пропущена [{title}]({uri})."}}, "skipto": {"description": "Переходит к определенной песне в очереди", "options": {"number": "Номер песни, к которой вы хотите перейти"}, "errors": {"invalid_number": "Пожалуйста, введите допустимое число."}, "messages": {"skipped_to": "Переход к песне номер {number}."}}, "stop": {"description": "Останавливает музыку и очищает очередь", "messages": {"stopped": "Музыка остановлена, очередь очищена."}}, "volume": {"description": "Устанавливает громкость проигрывателя", "options": {"number": "Громкость, которую вы хотите установить"}, "messages": {"invalid_number": "Пожалуйста, введите допустимое число.", "too_low": "Громкость не может быть ниже 0.", "too_high": "Громкость не может быть выше 200.  Вы хотите повредить слух или динамики?  Hmmm, я не думаю, что это хорошая идея.", "set": "Громкость установлена на {volume}"}}, "addsong": {"description": "Добавляет песню в плейлист", "options": {"playlist": "Плейлист, в который вы хотите добавить", "song": "Пе<PERSON>ня, которую вы хотите добавить"}, "messages": {"no_playlist": "Пожалуйста, укажите плейлист", "no_song": "Пожалуйста, укажите песню", "playlist_not_found": "Этот плейлист не существует", "no_songs_found": "Песни не найдены", "added": "Добавлено {count} песня(и) в {playlist}"}}, "create": {"description": "Создает плейлист", "options": {"name": "Название плейлиста"}, "messages": {"name_too_long": "Название плейлиста может содержать не более 50 символов.", "playlist_exists": "Плейлист с таким названием уже существует.  Пожалуйста, используйте другое название.", "playlist_created": "Плейлист **{name}** создан."}}, "delete": {"description": "Удаляет плейлист", "options": {"playlist": "Плейлист, который вы хотите удалить"}, "messages": {"playlist_not_found": "Этот плейлист не существует.", "playlist_deleted": "Плейлист **{playlistName}** удален."}}, "list": {"description": "Получает все плейлисты для пользователя", "options": {"user": "Пользователь, чьи плейлисты вы хотите получить"}, "messages": {"no_playlists": "У этого пользователя нет плейлистов.", "your": "<PERSON>а<PERSON>и", "playlists_title": "Плейлисты {username}", "error": "Произошла ошибка при получении плейлистов."}}, "load": {"description": "Загружает плейлист", "options": {"playlist": "Плейлист, который вы хотите загрузить"}, "messages": {"playlist_not_exist": "Этот плейлист не существует.", "playlist_empty": "Этот плейлист пуст.", "playlist_loaded": "Загруж<PERSON>н `{name}` с `{count}` песнями."}}, "removesong": {"description": "Удаляет песню из плейлиста", "options": {"playlist": "Плейлист, из которого вы хотите удалить", "song": "Песня, которую вы хотите удалить"}, "messages": {"provide_playlist": "Пожалуйста, укажите плейлист.", "provide_song": "Пожалуйста, укажите песню.", "playlist_not_exist": "Этот плейлист не существует.", "song_not_found": "Не найдена соответствующая песня.", "song_removed": "Удале<PERSON> {song} из {playlist}.", "error_occurred": "Произошла ошибка при удалении песни."}}, "steal": {"description": "Крадет плейлист у другого пользователя и добавляет его в свои плейлисты", "options": {"playlist": "Плейлист, который вы хотите украсть", "user": "Пользователь, у которого вы хотите украсть плейлист"}, "messages": {"provide_playlist": "Пожалуйста, введите название плейлиста.", "provide_user": "Пожалуйста, упомяните пользователя.", "playlist_not_exist": "Этот плейлист не существует для упомянутого пользователя.", "playlist_stolen": "Успешно украден плейлист `{playlist}` у {user}.", "error_occurred": "Произошла ошибка при краже плейлиста."}}, "lyrics": {"description": "Get's the lyrics of the currently playing track"}}, "buttons": {"invite": "Пригласить", "support": "Сервер поддержки", "previous": "Предыдущий", "resume": "Возобновить", "stop": "Остановить", "skip": "Пропустить", "loop": "<PERSON>и<PERSON><PERSON>", "errors": {"not_author": "Вы не можете использовать эту кнопку."}}, "player": {"errors": {"no_player": "В этом сервере нет активного проигрывателя.", "no_channel": "Вам нужно быть в голосовом канале, чтобы использовать эту команду.", "queue_empty": "Очередь пуста.", "no_previous": "В очереди нет предыдущих песен.", "no_song": "В очереди нет песен.", "already_paused": "Песня уже на паузе."}, "trackStart": {"now_playing": "Сейчас играет", "requested_by": "Запрошено {user}", "duration": "Длительность", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "not_connected_to_voice_channel": "Вы не подключены к <#{channel}>, чтобы использовать эти кнопки.", "need_dj_role": "Вам нужна роль DJ, чтобы использовать эту команду.", "previous_by": "Предыдущая {user}", "no_previous_song": "Нет предыдущей песни.", "paused_by": "Поставлено на паузу {user}", "resumed_by": "Возобновлено {user}", "skipped_by": "Пропущено {user}", "no_more_songs_in_queue": "В очереди нет больше песен.", "looping_by": "Зацикливание {user}", "looping_queue_by": "Зацикливание очереди {user}", "looping_off_by": "Зацикливание отключено {user}"}, "setupStart": {"now_playing": "Now Playing", "description": "[{title}]({uri}) by {author} • `[{length}]` - Requested by <@{requester}>", "error_searching": "There was an error while searching.", "no_results": "There were no results found.", "nothing_playing": "Nothing playing right now.", "queue_too_long": "The queue is too long. The maximum length is {maxQueueSize} songs.", "playlist_too_long": "The playlist is too long. The maximum length is {maxPlaylistSize} songs.", "added_to_queue": "Added [{title}]({uri}) to the queue.", "added_playlist_to_queue": "Added [{length}] songs from the playlist to the queue."}}, "event": {"interaction": {"setup_channel": "Вы не можете использовать эту команду в канале настройки.", "no_send_message": "У меня нет разрешения **`SendMessage`** в `{guild}`\nканал: {channel}.", "no_embed_links": "У меня нет разрешения **`EmbedLinks`**.", "no_permission": "У меня недостаточно прав для выполнения этой команды.", "no_user_permission": "У вас недостаточно прав для использования этой команды.", "no_voice_channel": "Вы должны быть подключены к голосовому каналу, чтобы использовать эту команду `{command}`.", "no_connect_permission": "У меня нет разрешения `CONNECT`, чтобы выполнить эту команду `{command}`.", "no_speak_permission": "У меня нет разрешения `SPEAK`, чтобы выполнить эту команду `{command}`.", "no_request_to_speak": "У меня нет разрешения `REQUEST TO SPEAK`, чтобы выполнить эту команду `{command}`.", "different_voice_channel": "Вы не подключены к {channel}, чтобы использовать эту команду `{command}`.", "no_music_playing": "Сейчас ничего не играет.", "no_dj_role": "Роль <PERSON> не установлена.", "no_dj_permission": "Вам нужна роль DJ, чтобы использовать эту команду.", "cooldown": "Пожалуйста, подождите {time} секунд, прежде чем использовать команду `{command}` снова.", "error": "Произошла ошибка: `{error}`"}, "message": {"prefix_mention": "Привет, мой префикс для этого сервера - `{prefix}`. Хотите больше информации?  Тогда введите `{prefix}help`\nБудьте в безопасности, будьте круты!", "no_send_message": "У меня нет разрешения **`SendMessage`** в `{guild}`\nканал: {channel}.", "no_embed_links": "У меня нет разрешения **`EmbedLinks`**.", "no_permission": "У меня недостаточно прав для выполнения этой команды.", "no_user_permission": "У вас недостаточно прав для использования этой команды.", "no_voice_channel": "Вы должны быть подключены к голосовому каналу, чтобы использовать эту команду `{command}`.", "no_connect_permission": "У меня нет разрешения `CONNECT`, чтобы выполнить эту команду `{command}`.", "no_speak_permission": "У меня нет разрешения `SPEAK`, чтобы выполнить эту команду `{command}`.", "no_request_to_speak": "У меня нет разрешения `REQUEST TO SPEAK`, чтобы выполнить эту команду `{command}`.", "different_voice_channel": "Вы не подключены к {channel}, чтобы использовать эту команду `{command}`.", "no_music_playing": "Сейчас ничего не играет.", "no_dj_role": "Роль <PERSON> не установлена.", "no_dj_permission": "Вам нужна роль DJ, чтобы использовать эту команду.", "missing_arguments": "Отсутствующие аргументы", "missing_arguments_description": "Пожалуйста, укажите необходимые аргументы для команды `{command}`.\n\nПримеры:\n{examples}", "syntax_footer": "Синтаксис: [] = необязательно, <> = обязательно", "cooldown": "Пожалуйста, подождите {time} секунд, прежде чем использовать команду `{command}` снова.", "no_mention_everyone": "Вы не можете использовать эту команду с @everyone или @here.", "error": "Произошла ошибка: `{error}`", "no_voice_channel_queue": "Вы не подключены к голосовому каналу, чтобы добавить песни в очередь.", "no_permission_connect_speak": "У меня недостаточно прав, чтобы подключиться/говорить в <#{channel}>.", "different_voice_channel_queue": "Вы не подключены к <#{channel}>, чтобы добавить песни в очередь."}, "setupButton": {"no_voice_channel_button": "Вы не подключены к голосовому каналу, чтобы использовать эту кнопку.", "different_voice_channel_button": "Вы не подключены к {channel}, чтобы использовать эти кнопки.", "now_playing": "Сейчас играет", "live": "В ПРЯМОМ ЭФИРЕ", "requested_by": "Запрошено <@{requester}>", "no_dj_permission": "Вам нужна роль DJ, чтобы использовать эту команду.", "volume_set": "Громкость установлена на {vol}%", "volume_footer": "Громкость: {vol}%", "paused": "Пауза", "resumed": "Возобновлено", "pause_resume": "{name} музыку.", "pause_resume_footer": "{name} {displayName}", "no_music_to_skip": "Нет музыки для пропуска.", "skipped": "Музыка пропущена.", "skipped_footer": "Пропущено {displayName}", "stopped": "Музыка остановлена.", "stopped_footer": "Остановлено {displayName}", "nothing_playing": "Сейчас ничего не играет", "loop_set": "Цикл установлен на {loop}.", "loop_footer": "Цикл установлен на {loop} {displayName}", "shuffled": "Очередь перемешана.", "no_previous_track": "Нет предыдущей дорожки.", "playing_previous": "Проигрывается предыдущая дорожка.", "previous_footer": "Проигрывается предыдущая дорожка {displayName}", "rewinded": "Музыка перемотана назад.", "rewind_footer": "Перемотано назад {displayName}", "forward_limit": "Вы не можете перемотать музыку вперед больше, чем длина песни.", "forwarded": "Музыка перемотана вперед.", "forward_footer": "Перемотано вперед {displayName}", "button_not_available": "Эта кнопка недоступна.", "no_music_playing": "Сейчас ничего не играет."}}, "Evaluate code": "Evaluate code", "Leave a guild": "Leave a guild", "List all guilds the bot is in": "List all guilds the bot is in", "Restart the bot": "Restart the bot"}