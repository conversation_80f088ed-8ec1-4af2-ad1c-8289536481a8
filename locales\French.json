{"cmd": {"247": {"description": "Configure le bot pour qu'il reste dans le canal vocal", "errors": {"not_in_voice": "Vous devez être dans un canal vocal pour utiliser cette commande.", "generic": "Une erreur s'est produite lors de l'exécution de cette commande."}, "messages": {"disabled": "`✅` | Le mode 24/7 a été `DÉSACTIVÉ`", "enabled": "`✅` | Le mode 24/7 a été `ACTIVÉ`. \n**Le bot ne quittera pas le canal vocal même s'il n'y a personne dans le canal vocal.**"}}, "ping": {"description": "Affiche le ping du bot.", "content": "Ping en cours...", "bot_latency": "Latence du bot", "api_latency": "Latence de l'API", "requested_by": "<PERSON><PERSON><PERSON> par {author}"}, "lavalink": {"description": "Affiche les statistiques actuelles de Lavalink.", "title": "Statistiques Lavalink", "content": "Joueurs: {players}\nJo<PERSON><PERSON> en cours: {playingPlayers}\nTemps de fonctionnement: {uptime}\nNoyaux: {cores} Noyau(x)\nUtilisation de la mémoire: {used} / {reservable}\nCharge du système: {systemLoad}%\nCharge Lavalink: {lavalinkLoad}%"}, "invite": {"description": "Obt<PERSON>z le lien d'invitation du bot.", "content": "Vous pouvez m'inviter en cliquant sur le bouton ci-dessous. Tout bug ou panne ? Rejoignez le serveur d'assistance !"}, "help": {"description": "Affiche le menu d'aide.", "options": {"command": "La commande sur laquelle vous souhaitez obtenir des informations"}, "content": "Salut ! Je suis {bot}, un bot de musique créé avec [Lavamusic](https://github.com/appujet/lavamusic) et Discord. Vous pouvez utiliser `{prefix}help <command>` pour obtenir plus d'informations sur une commande.", "title": "<PERSON><PERSON> d'aide", "not_found": "La commande `{cmdName}` n'existe pas.", "help_cmd": "**Description:** {description}\n**Utilisation:** {usage}\n**Exemples:** {examples}\n**Alias:** {aliases}\n**Catégorie:** {category}\n**Refroidissement:** {cooldown} secondes\n**Permissions de l'utilisateur:** {premUser}\n**Permissions du bot:** {premBot}\n**Développeur uniquement:** {dev}\n**Commande Slash:** {slash}\n**Args:** {args}\n**Lecteur:** {player}\n**DJ:** {dj}\n**Permissions DJ:** {djPerm}\n**Voix:** {voice}", "footer": "Utilisez {prefix}help <command> pour plus d'informations sur une commande"}, "botinfo": {"description": "Informations sur le bot", "content": "Informations sur le bot:\n- **Système d'exploitation**: {osInfo}\n- **Temps de fonctionnement**: {osUptime}\n- **Nom d'hôte**: {osHostname}\n- **Architecture du processeur**: {cpuInfo}\n- **Utilisation du processeur**: {cpuUsed}%\n- **Utilisation de la mémoire**: {memUsed}MB / {memTotal}GB\n- **Version de Node**: {nodeVersion}\n- **Version de Discord**: {discordJsVersion}\n- **Connecté à** {guilds} guildes, {channels} canaux et {users} utilisateurs\n- **Nombre total de commandes**: {commands}"}, "about": {"description": "Affiche des informations sur le bot", "fields": {"creator": "<PERSON><PERSON><PERSON><PERSON>", "repository": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "support": "Assistance", "description": "Il voulait vraiment faire son premier projet open source pour plus d'expérience en codage. Dans ce projet, il a été mis au défi de faire un projet avec moins de bugs. J'espère que vous apprécierez l'utilisation de LavaMusic !"}}, "dj": {"description": "<PERSON><PERSON><PERSON> le mode DJ et les rôles associés", "errors": {"provide_role": "Veuillez fournir un rôle.", "no_roles": "Le rôle DJ est vide.", "invalid_subcommand": "Veuillez fournir une sous-commande valide."}, "messages": {"role_exists": "Le rôle DJ <@&{roleId}> est déjà ajouté.", "role_added": "Le rôle DJ <@&{roleId}> a été ajouté.", "role_not_found": "Le rôle DJ <@&{roleId}> n'est pas ajouté.", "role_removed": "Le rôle DJ <@&{roleId}> a été supprimé.", "all_roles_cleared": "Tous les rôles DJ ont été supprimés.", "toggle": "Le mode DJ a <PERSON><PERSON> basculé vers {status}."}, "options": {"add": "Le rôle DJ que vous souhaitez ajouter", "remove": "Le rôle DJ que vous souhaitez supprimer", "clear": "Efface tous les rôles DJ", "toggle": "Active/dés<PERSON> le rôle DJ", "role": "Le rôle DJ"}, "subcommands": "Sous-commandes"}, "prefix": {"description": "Affiche ou définit le préfixe du bot", "errors": {"prefix_too_long": "Le préfixe ne peut pas dépasser 3 caractères."}, "messages": {"current_prefix": "Le préfixe pour ce serveur est `{prefix}`", "prefix_set": "Le préfixe pour ce serveur est maintenant `{prefix}`", "prefix_reset": "Le préfixe pour ce serveur est maintenant `{prefix}`"}, "options": {"set": "Définit le préfixe", "prefix": "Le préfixe que vous souhaitez définir", "reset": "Réinitialise le préfixe à celui par défaut"}}, "setup": {"description": "Configure le bot", "errors": {"channel_exists": "Le canal de demande de chanson existe déjà.", "channel_not_exists": "Le canal de demande de chanson n'existe pas.", "channel_delete_fail": "Le canal de demande de chanson a été supprimé. Si le canal n'est pas supprimé normalement, veuillez le supprimer vous-même."}, "messages": {"channel_created": "Le canal de demande de chanson a été créé dans <#{channelId}>.", "channel_deleted": "Le canal de demande de chanson a été supprimé.", "channel_info": "Le canal de demande de chanson est <#{channelId}>."}, "options": {"create": "Crée le canal de demande de chanson", "delete": "Supprime le canal de demande de chanson", "info": "Affiche le canal de demande de chanson"}}, "8d": {"description": "Activer/dés<PERSON>r le filtre 8d", "messages": {"filter_enabled": "`✅` | Le filtre 8D a été `ACTIVÉ`.", "filter_disabled": "`✅` | Le filtre 8D a été `DÉSACTIVÉ`."}}, "bassboost": {"description": "Activer/dés<PERSON>r le filtre de basses", "messages": {"filter_enabled": "`✅` | Le filtre de basses a été `ACTIVÉ`. \n**Attention, écouter trop fort peut endommager votre audition !**", "filter_disabled": "`✅` | Le filtre de basses a été `DÉSACTIVÉ`."}, "options": {"level": "The bassboost level you want to set"}}, "distortion": {"description": "Active/désactive le filtre de distorsion", "messages": {"filter_enabled": "`✅` | Le filtre de distorsion a été `ACTIVÉ`.", "filter_disabled": "`✅` | Le filtre de distorsion a été `DÉSACTIVÉ`."}}, "karaoke": {"description": "Active/désactive le filtre de karaoké", "messages": {"filter_enabled": "`✅` | Le filtre de karaoké a été `ACTIVÉ`.", "filter_disabled": "`✅` | Le filtre de karaoké a été `DÉSACTIVÉ`."}}, "lowpass": {"description": "Active/désactive le filtre passe-bas", "messages": {"filter_enabled": "`✅` | Le filtre passe-bas a été `ACTIVÉ`.", "filter_disabled": "`✅` | Le filtre passe-bas a été `DÉSACTIVÉ`."}}, "nightcore": {"description": "Active/désactive le filtre nightcore", "messages": {"filter_enabled": "`✅` | Le filtre nightcore a été `ACTIVÉ`.", "filter_disabled": "`✅` | Le filtre nightcore a été `DÉSACTIVÉ`."}}, "pitch": {"description": "Active/désactive le filtre de hauteur", "options": {"pitch": "Le nombre auquel vous souhaitez définir la hauteur (entre 0,5 et 5)"}, "errors": {"invalid_number": "Veuillez fournir un nombre valide compris entre 0,5 et 5."}, "messages": {"pitch_set": "`✅` | La hauteur a été définie sur **{pitch}**."}}, "rate": {"description": "<PERSON><PERSON><PERSON> le rythm<PERSON> de la chanson", "options": {"rate": "Le nombre auquel vous souhaitez définir le rythme (entre 0,5 et 5)"}, "errors": {"invalid_number": "Veuillez fournir un nombre valide compris entre 0,5 et 5."}, "messages": {"rate_set": "`✅` | Le rythme a été défini sur **{rate}**."}}, "reset": {"description": "Réinitialise les filtres actifs", "messages": {"filters_reset": "`✅` | Les filtres ont été réinitialisés."}}, "rotation": {"description": "Active/désactive le filtre de rotation", "messages": {"enabled": "`✅` | Le filtre de rotation a été `ACTIVÉ`.", "disabled": "`✅` | Le filtre de rotation a été `DÉSACTIVÉ`."}}, "speed": {"description": "Modifie la vitesse de la chanson", "options": {"speed": "La vitesse que vous souhaitez définir"}, "messages": {"invalid_number": "Veuillez fournir un nombre valide compris entre 0,5 et 5.", "set_speed": "`✅` | La vitesse a été définie sur **{speed}**."}}, "tremolo": {"description": "Active/désactive le filtre de trémolo", "messages": {"enabled": "`✅` | Le filtre de trémolo a été `ACTIVÉ`.", "disabled": "`✅` | Le filtre de trémolo a été `DÉSACTIVÉ`."}}, "vibrato": {"description": "Active/désactive le filtre de vibrato", "messages": {"enabled": "`✅` | Le filtre de vibrato a été `ACTIVÉ`.", "disabled": "`✅` | Le filtre de vibrato a été `DÉSACTIVÉ`."}}, "autoplay": {"description": "Active/désactive la lecture automatique", "messages": {"enabled": "`✅` | La lecture automatique a été `ACTIVÉ`.", "disabled": "`✅` | La lecture automatique a été `DÉSACTIVÉ`."}}, "clearqueue": {"description": "Efface la file d'attente", "messages": {"cleared": "La file d'attente a été effacée."}}, "grab": {"description": "Récupère la chanson en cours de lecture sur votre DM", "content": "**Durée:** {length}\n**Demandé par:** <@{requester}>\n**Lien:** [Cliquez ici]({uri})", "check_dm": "Veuillez vérifier votre DM.", "dm_failed": "Je n'ai pas pu vous envoyer de DM."}, "join": {"description": "Rejoint le canal vocal", "already_connected": "Je suis déjà connecté à <#{channelId}>.", "no_voice_channel": "Vous devez être dans un canal vocal pour utiliser cette commande.", "joined": "Rejoint avec succès <#{channelId}>."}, "leave": {"description": "<PERSON><PERSON><PERSON> le canal vocal", "left": "Quitte avec succès <#{channelId}>.", "not_in_channel": "Je ne suis pas dans un canal vocal."}, "loop": {"description": "Boucle la chanson actuelle ou la file d'attente", "looping_song": "**<PERSON><PERSON><PERSON> de la chanson.**", "looping_queue": "**<PERSON><PERSON><PERSON> de la file d'attente.**", "looping_off": "**La boucle est maintenant désactivée.**"}, "nowplaying": {"description": "Affiche la chanson en cours de lecture", "now_playing": "En cours de lecture", "track_info": "[{title}]({uri}) - Demandé par: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "Met en pause la chanson actuelle", "successfully_paused": "<PERSON><PERSON> mise en pause avec succès."}, "play": {"description": "Lit une chanson de YouTube, Spotify ou http", "options": {"song": "La chanson que vous souhaitez lire"}, "loading": "Chargement...", "errors": {"search_error": "Une erreur s'est produite lors de la recherche.", "no_results": "Aucun résultat trouvé.", "queue_too_long": "La file d'attente est trop longue. La longueur maximale est de {maxQueueSize} chansons.", "playlist_too_long": "La playlist est trop longue. La longueur maximale est de {maxPlaylistSize} chansons."}, "added_to_queue": "Ajouté [{title}]({uri}) à la file d'attente.", "added_playlist_to_queue": "Ajouté {length} chansons à la file d'attente."}, "playnext": {"description": "Ajoute la chanson à jouer ensuite dans la file d'attente", "options": {"song": "La chanson que vous souhaitez lire"}, "loading": "Chargement...", "errors": {"search_error": "Une erreur s'est produite lors de la recherche.", "no_results": "Aucun résultat trouvé.", "queue_too_long": "La file d'attente est trop longue. La longueur maximale est de {maxQueueSize} chansons.", "playlist_too_long": "La playlist est trop longue. La longueur maximale est de {maxPlaylistSize} chansons."}, "added_to_play_next": "Ajou<PERSON> [{title}]({uri}) à jouer ensuite dans la file d'attente.", "added_playlist_to_play_next": "Ajouté {length} chansons à jouer ensuite dans la file d'attente."}, "queue": {"description": "Affiche la file d'attente actuelle", "now_playing": "En cours de lecture: [{title}]({uri}) - <PERSON><PERSON><PERSON> par: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "live": "EN DIRECT", "track_info": "{index}. [{title}]({uri}) - Demandé par: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "title": "File d'attente", "page_info": "Page {index} sur {total}"}, "remove": {"description": "Supprime une chanson de la file d'attente", "options": {"song": "Le numéro de la chanson que vous souhaitez supprimer"}, "errors": {"no_songs": "Il n'y a aucune chanson dans la file d'attente.", "invalid_number": "Veuillez fournir un numéro de chanson valide."}, "messages": {"removed": "<PERSON><PERSON> numéro {songNumber} supprimée de la file d'attente."}}, "replay": {"description": "Relance la piste actuelle", "errors": {"not_seekable": "Impossible de rejouer cette piste car elle n'est pas cherchable."}, "messages": {"replaying": "Relance la piste actuelle."}}, "resume": {"description": "Reprend la chanson actuelle", "errors": {"not_paused": "Le lecteur n'est pas en pause."}, "messages": {"resumed": "Le lecteur a repris."}}, "search": {"description": "Recherche une chanson", "options": {"song": "La chanson que vous souhaitez rechercher"}, "errors": {"no_results": "Aucun résultat trouvé.", "search_error": "Une erreur s'est produite lors de la recherche."}, "messages": {"added_to_queue": "Ajouté [{title}]({uri}) à la file d'attente."}}, "seek": {"description": "Cherche à un moment précis dans la chanson", "options": {"duration": "La durée à laquelle vous souhaitez chercher"}, "errors": {"invalid_format": "Format de temps incorrect. Exemples : seek 1m, seek 1h 30m", "not_seekable": "Cette piste n'est pas cherchable.", "beyond_duration": "Impossible de chercher au-delà de la durée de la chanson de {length}."}, "messages": {"seeked_to": "Cher<PERSON><PERSON> à {duration}"}}, "shuffle": {"description": "Mélange la file d'attente", "messages": {"shuffled": "File d'attente mélangée."}}, "skip": {"description": "Passe la chanson actuelle", "messages": {"skipped": "Passé [{title}]({uri})."}}, "skipto": {"description": "Passe à une chanson spécifique dans la file d'attente", "options": {"number": "Le numéro de la chanson à laquelle vous souhaitez passer"}, "errors": {"invalid_number": "Veuillez fournir un nombre valide."}, "messages": {"skipped_to": "Passé à la chanson numéro {number}."}}, "stop": {"description": "Arrête la musique et efface la file d'attente", "messages": {"stopped": "Musique arrêtée et file d'attente effacée."}}, "volume": {"description": "Définit le volume du lecteur", "options": {"number": "Le volume que vous souhaitez définir"}, "messages": {"invalid_number": "Veuillez fournir un nombre valide.", "too_low": "Le volume ne peut pas être inférieur à 0.", "too_high": "Le volume ne peut pas être supérieur à 200. Voulez-vous endommager votre audition ou vos haut-parleurs ? Hmmm, je ne pense pas que ce soit une bonne idée.", "set": "Volume défini sur {volume}"}}, "addsong": {"description": "Ajoute une chanson à la playlist", "options": {"playlist": "La playlist à laquelle vous souhaitez ajouter", "song": "La chanson que vous souhaitez ajouter"}, "messages": {"no_playlist": "<PERSON><PERSON><PERSON><PERSON> fournir une playlist", "no_song": "Veuillez fournir une chanson", "playlist_not_found": "Cette playlist n'existe pas", "no_songs_found": "<PERSON><PERSON>ne chanson trouvée", "added": "A<PERSON><PERSON> {count} chanson(s) à {playlist}"}}, "create": {"description": "<PERSON><PERSON><PERSON> une playlist", "options": {"name": "Le nom de la playlist"}, "messages": {"name_too_long": "Les noms de playlist ne peuvent pas dépasser 50 caractères.", "playlist_exists": "Une playlist portant ce nom existe déjà. Veuillez utiliser un nom différent.", "playlist_created": "La playlist **{name}** a <PERSON><PERSON> créée."}}, "delete": {"description": "Supprime une playlist", "options": {"playlist": "La playlist que vous souhaitez supprimer"}, "messages": {"playlist_not_found": "Cette playlist n'existe pas.", "playlist_deleted": "Playlist **{playlistName}** supprimée."}}, "list": {"description": "Récupère toutes les playlists de l'utilisateur", "options": {"user": "L'utilisateur dont vous souhaitez récupérer les playlists"}, "messages": {"no_playlists": "Cet utilisateur n'a pas de playlist.", "your": "Vos", "playlists_title": "Playlists de {username}", "error": "Une erreur s'est produite lors de la récupération des playlists."}}, "load": {"description": "Charge une playlist", "options": {"playlist": "La playlist que vous souhaitez charger"}, "messages": {"playlist_not_exist": "Cette playlist n'existe pas.", "playlist_empty": "Cette playlist est vide.", "playlist_loaded": "Chargement de `{name}` avec `{count}` chansons."}}, "removesong": {"description": "Supprime une chanson de la playlist", "options": {"playlist": "La playlist dont vous souhaitez supprimer", "song": "La chanson que vous souhaitez supprimer"}, "messages": {"provide_playlist": "<PERSON><PERSON><PERSON><PERSON> fournir une playlist.", "provide_song": "<PERSON>euillez fournir une chanson.", "playlist_not_exist": "Cette playlist n'existe pas.", "song_not_found": "Aucune chanson correspondante trouvée.", "song_removed": "Suppression de {song} de {playlist}.", "error_occurred": "Une erreur s'est produite lors de la suppression de la chanson."}}, "steal": {"description": "Vole une playlist à un autre utilisateur et l'ajoute à vos playlists", "options": {"playlist": "La playlist que vous souhaitez voler", "user": "L'utilisateur dont vous souhaitez voler la playlist"}, "messages": {"provide_playlist": "Veuillez fournir un nom de playlist.", "provide_user": "Veuillez mentionner un utilisateur.", "playlist_not_exist": "Cette playlist n'existe pas pour l'utilisateur mentionné.", "playlist_stolen": "Vole avec succès la playlist `{playlist}` à {user}.", "error_occurred": "Une erreur s'est produite lors du vol de la playlist."}}, "language": {"description": "Définit la langue du bot", "invalid_language": "Veuillez fournir une langue valide. Exemple : `EnglishUS` pour l'anglais (États-Unis)\n\nVous pouvez trouver la liste des langues prises en charge [ici](https://discord.com/developers/docs/reference#locales)\n\n**Langues disponibles:**\n{languages}", "already_set": "La langue est déjà définie sur `{language}`", "not_set": "La langue n'est pas définie.", "set": "`✅` | La langue a été définie sur `{language}`", "reset": "`✅` | La langue a été réinitialisée.", "options": {"set": "Définit la langue du bot", "language": "La langue que vous souhaitez définir", "reset": "Change la langue par défaut"}}, "lyrics": {"description": "Get's the lyrics of the currently playing track"}, "fairplay": {"description": "Set the bot to play music fairly"}, "playlocal": {"description": "Plays an your local audio files", "options": {"file": "The audio file you want to play"}}, "say": {"description": "Quick text to speech conversion", "options": {"text": "The text to say"}}, "speak": {"description": "Convert text to speech with advanced voice options", "options": {"text": "The text to convert to speech", "voice": "The voice to use for speech synthesis", "speed": "The speed of speech (0.5 to 2.0)"}}, "mluvit": {"description": "Czech text to speech conversion", "options": {"text": "Text to convert to Czech speech", "voice": "Czech voice to use (<PERSON><PERSON><PERSON> or <PERSON><PERSON>)", "speed": "Speech speed (0.5 to 2.0)"}}}, "buttons": {"invite": "Inviter", "support": "Serveur d'assistance", "previous": "Précédent", "resume": "Reprendre", "stop": "<PERSON><PERSON><PERSON><PERSON>", "skip": "Passer", "loop": "<PERSON><PERSON><PERSON>", "errors": {"not_author": "Vous ne pouvez pas utiliser ce bouton."}}, "player": {"errors": {"no_player": "Il n'y a pas de lecteur actif dans cette guilde.", "no_channel": "Vous devez être dans un canal vocal pour utiliser cette commande.", "queue_empty": "La file d'attente est vide.", "no_previous": "Il n'y a pas de chansons précédentes dans la file d'attente.", "no_song": "Il n'y a pas de chanson dans la file d'attente.", "already_paused": "La chanson est déjà en pause."}, "trackStart": {"now_playing": "En cours de lecture", "requested_by": "<PERSON><PERSON><PERSON> par {user}", "duration": "<PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON>", "not_connected_to_voice_channel": "Vous n'êtes pas connecté à <#{channel}> pour utiliser ces boutons.", "need_dj_role": "V<PERSON> devez avoir le rôle DJ pour utiliser cette commande.", "previous_by": "Précédent par {user}", "no_previous_song": "Il n'y a pas de chanson précédente.", "paused_by": "Mis en pause par {user}", "resumed_by": "<PERSON>ris par {user}", "skipped_by": "Passé par {user}", "no_more_songs_in_queue": "Il n'y a plus de chanson dans la file d'attente.", "looping_by": "Boucle par {user}", "looping_queue_by": "Bo<PERSON>le de la file d'attente par {user}", "looping_off_by": "<PERSON><PERSON><PERSON> désactivée par {user}"}, "setupStart": {"now_playing": "Now Playing", "description": "[{title}]({uri}) by {author} • `[{length}]` - Requested by <@{requester}>", "error_searching": "There was an error while searching.", "no_results": "There were no results found.", "nothing_playing": "Nothing playing right now.", "queue_too_long": "The queue is too long. The maximum length is {maxQueueSize} songs.", "playlist_too_long": "The playlist is too long. The maximum length is {maxPlaylistSize} songs.", "added_to_queue": "Added [{title}]({uri}) to the queue.", "added_playlist_to_queue": "Added [{length}] songs from the playlist to the queue."}}, "event": {"interaction": {"setup_channel": "Vous ne pouvez pas utiliser cette commande dans le canal de configuration.", "no_send_message": "Je n'ai pas la permission **`SendMessage`** dans `{guild}`\ncanal: {channel}.", "no_embed_links": "Je n'ai pas la permission **`EmbedLinks`**.", "no_permission": "Je n'ai pas suffisamment de permissions pour exécuter cette commande.", "no_user_permission": "Vous n'avez pas suffisamment de permissions pour utiliser cette commande.", "no_voice_channel": "Vous devez être connecté à un canal vocal pour utiliser cette commande `{command}`.", "no_connect_permission": "Je n'ai pas la permission `CONNECT` pour exécuter cette commande `{command}`.", "no_speak_permission": "Je n'ai pas la permission `SPEAK` pour exécuter cette commande `{command}`.", "no_request_to_speak": "Je n'ai pas la permission `DEMANDE DE PARLER` pour exécuter cette commande `{command}`.", "different_voice_channel": "Vous n'êtes pas connecté à {channel} pour utiliser cette commande `{command}`.", "no_music_playing": "<PERSON><PERSON> ne joue en ce moment.", "no_dj_role": "Le rôle DJ n'est pas défini.", "no_dj_permission": "V<PERSON> devez avoir le rôle DJ pour utiliser cette commande.", "cooldown": "<PERSON><PERSON><PERSON><PERSON> attendre {time} seconde(s) de plus avant de réutiliser la commande `{command}`.", "error": "Une erreur s'est produite : `{error}`"}, "message": {"prefix_mention": "Hé, mon préfixe pour ce serveur est `{prefix}`. Vous voulez plus d'infos ? alors faites `{prefix}help`\nRestez en sécurité, restez génial !", "no_send_message": "Je n'ai pas la permission **`SendMessage`** dans `{guild}`\ncanal: {channel}.", "no_embed_links": "Je n'ai pas la permission **`EmbedLinks`**.", "no_permission": "Je n'ai pas suffisamment de permissions pour exécuter cette commande.", "no_user_permission": "Vous n'avez pas suffisamment de permissions pour utiliser cette commande.", "no_voice_channel": "Vous devez être connecté à un canal vocal pour utiliser cette commande `{command}`.", "no_connect_permission": "Je n'ai pas la permission `CONNECT` pour exécuter cette commande `{command}`.", "no_speak_permission": "Je n'ai pas la permission `SPEAK` pour exécuter cette commande `{command}`.", "no_request_to_speak": "Je n'ai pas la permission `DEMANDE DE PARLER` pour exécuter cette commande `{command}`.", "different_voice_channel": "Vous n'êtes pas connecté à {channel} pour utiliser cette commande `{command}`.", "no_music_playing": "<PERSON><PERSON> ne joue en ce moment.", "no_dj_role": "Le rôle DJ n'est pas défini.", "no_dj_permission": "V<PERSON> devez avoir le rôle DJ pour utiliser cette commande.", "missing_arguments": "Arguments manquants", "missing_arguments_description": "Veuillez fournir les arguments requis pour la commande `{command}`.\n\nExemples:\n{examples}", "syntax_footer": "Syntaxe: [] = facultatif, <> = requis", "cooldown": "<PERSON><PERSON><PERSON><PERSON> attendre {time} seconde(s) de plus avant de réutiliser la commande `{command}`.", "no_mention_everyone": "Vous ne pouvez pas utiliser cette commande avec tout le monde ou ici.", "error": "Une erreur s'est produite : `{error}`", "no_voice_channel_queue": "Vous n'êtes pas connecté à un canal vocal pour mettre des chansons en file d'attente.", "no_permission_connect_speak": "Je n'ai pas suffisamment de permissions pour me connecter/parler dans <#{channel}>.", "different_voice_channel_queue": "Vous n'êtes pas connecté à <#{channel}> pour mettre des chansons en file d'attente."}, "setupButton": {"no_voice_channel_button": "Vous n'êtes pas connecté à un canal vocal pour utiliser ce bouton.", "different_voice_channel_button": "Vous n'êtes pas connecté à {channel} pour utiliser ces boutons.", "now_playing": "En cours de lecture", "live": "EN DIRECT", "requested_by": "Demandé par <@{requester}>", "no_dj_permission": "V<PERSON> devez avoir le rôle DJ pour utiliser cette commande.", "volume_set": "Volume défini sur {vol}%", "volume_footer": "Volume: {vol}%", "paused": "Mis en pause", "resumed": "<PERSON><PERSON>", "pause_resume": "{name} la musique.", "pause_resume_footer": "{name} par {displayName}", "no_music_to_skip": "Il n'y a pas de musique à passer.", "skipped": "Musique passée.", "skipped_footer": "Passé par {displayName}", "stopped": "Musique arrêtée.", "stopped_footer": "<PERSON>rr<PERSON><PERSON> par {displayName}", "nothing_playing": "<PERSON><PERSON> ne joue en ce moment", "loop_set": "<PERSON><PERSON><PERSON> dé<PERSON>ie sur {loop}.", "loop_footer": "<PERSON><PERSON><PERSON> définie sur {loop} par {displayName}", "shuffled": "File d'attente mélangée.", "no_previous_track": "Il n'y a pas de piste précédente.", "playing_previous": "Lecture de la piste précédente.", "previous_footer": "Lecture de la piste précédente par {displayName}", "rewinded": "Rembobinage de la musique.", "rewind_footer": "<PERSON><PERSON><PERSON><PERSON> par {displayName}", "forward_limit": "Vous ne pouvez pas avancer la musique plus que la longueur de la chanson.", "forwarded": "Musique avancée.", "forward_footer": "<PERSON><PERSON><PERSON> par {displayName}", "button_not_available": "Ce bouton n'est pas disponible.", "no_music_playing": "<PERSON><PERSON> ne joue en ce moment."}}, "Evaluate code": "Evaluate code", "Leave a guild": "Leave a guild", "List all guilds the bot is in": "List all guilds the bot is in", "Restart the bot": "Restart the bot", "The loop mode you want to set": "The loop mode you want to set"}