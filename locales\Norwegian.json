{"cmd": {"247": {"description": "Setter boten til å forbli i talekanalen", "errors": {"not_in_voice": "Du må være i en talekanal for å bruke denne kommandoen.", "generic": "En feil oppstod under forsø<PERSON> på å utføre denne kommandoen."}, "messages": {"disabled": "`✅` | 24/7-modus er `DEAKTIVERT`", "enabled": "`✅` | 24/7-modus er `AKTIVERT`. \n**Boten vil ikke forlate talekanalen selv om det ikke er noen i den.**"}}, "ping": {"description": "Viser botens ping.", "content": "Pinger...", "bot_latency": "Botforsinkelse", "api_latency": "API-forsinkelse", "requested_by": "Forespurt av {author}"}, "lavalink": {"description": "Viser gjeldende Lavalink-statistikk.", "title": "Lavalink-statistikk", "content": "Spillere: {players}\nSpillende spillere: {playingPlayers}\nOppetid: {uptime}\nKjerner: {cores} Kjerne(r)\nMinnebruk: {used} / {reservable}\nSystembelastning: {systemLoad}%\nLavalink-belastning: {lavalinkLoad}%"}, "invite": {"description": "<PERSON><PERSON> bot<PERSON>jonslenken.", "content": "Du kan invitere meg ved å klikke på knappen nedenfor. Eventuelle feil eller avbrudd? Bli med på støtteserveren!"}, "help": {"description": "Viser hjelpemenyen.", "options": {"command": "Kommandoen du vil ha informasjon om"}, "content": "Hei der! Jeg er {bot}, en musikkbot laget med [Lavamusic](https://github.com/appujet/lavamusic) og Discord. Du kan bruke `{prefix}help <kommando>` for å få mer informasjon om en kommando.", "title": "Hjelpemeny", "not_found": "Kommandoen `{cmdName}` finnes ikke.", "help_cmd": "**Beskrivelse:** {description}\n**Bruk:** {usage}\n**Eksempler:** {examples}\n**Aliaser:** {aliases}\n**Kategori:** {category}\n**Nedkjøling:** {cooldown} sekunder\n**Tillatelser:** {premUser}\n**Bo<PERSON><PERSON>telser:** {premBot}\n**Kun for utviklere:** {dev}\n**Skråstrekkommando:** {slash}\n**Argumenter:** {args}\n**Spiller:** {player}\n**DJ:** {dj}\n**DJ-tillatelser:** {djPerm}\n**Stemme:** {voice}", "footer": "Bruk {prefix}help <kommando> for mer informasjon om en kommando"}, "botinfo": {"description": "Informasjon om boten", "content": "Botinformasjon:\n- **Operativsystem**: {osInfo}\n- **Oppetid**: {osUptime}\n- **Vertsnavn**: {osHostname}\n- **CPU-arkitektur**: {cpuInfo}\n- **CPU-bruk**: {cpuUsed}%\n- **Minnebruk**: {memUsed}MB / {memTotal}GB\n- **Nodeversjon**: {nodeVersion}\n- **Discord-versjon**: {discordJsVersion}\n- **Tilkoblet** {guilds} servere, {channels} kanaler og {users} brukere\n- **Totalt antall kommandoer**: {commands}"}, "about": {"description": "Viser informasjon om boten", "fields": {"creator": "<PERSON><PERSON><PERSON>", "repository": "<PERSON><PERSON><PERSON>", "support": "<PERSON><PERSON><PERSON>", "description": "Han ville virkelig lage sitt første åpen kildekode-prosjekt noensinne for mer kodingsopplevelse. I dette prosjektet ble han utfordret til å lage et prosjekt med færre feil. Håper du liker å bruke LavaMusic!"}}, "dj": {"description": "Administrer DJ-modus og tilknyttede roller", "errors": {"provide_role": "<PERSON><PERSON> en rolle.", "no_roles": "DJ-rollen er tom.", "invalid_subcommand": "Angi en gyldig underkommando."}, "messages": {"role_exists": "DJ-rollen <@&{roleId}> er allerede lagt til.", "role_added": "DJ-rollen <@&{roleId}> er lagt til.", "role_not_found": "DJ-rollen <@&{roleId}> er ikke lagt til.", "role_removed": "DJ-rollen <@&{roleId}> er fjernet.", "all_roles_cleared": "Alle DJ-roller er fjernet.", "toggle": "DJ-modusen er endret til {status}."}, "options": {"add": "DJ-rollen du vil legge til", "remove": "DJ-roll<PERSON> du vil fjerne", "clear": "<PERSON><PERSON><PERSON> alle DJ-roller", "toggle": "Veksler DJ-rollen", "role": "DJ-rollen"}, "subcommands": "Underkommandoer"}, "language": {"description": "<PERSON><PERSON> språ<PERSON> for boten", "invalid_language": "<PERSON><PERSON> et gyldig språk. Eksempel: `EnglishUS` for engelsk (USA)\n\n<PERSON> finner listen over st<PERSON><PERSON><PERSON> språk [her](https://discord.com/developers/docs/reference#locales)\n\n**Tilgjengelige språk:**\n{languages}", "already_set": "Språket er allerede satt til `{language}`", "not_set": "Språket er ikke angitt.", "set": "`✅` | Språket er satt til `{language}`", "reset": "`✅` | Språket er tilbakestilt til standardspråket.", "options": {"set": "<PERSON><PERSON> språ<PERSON> for boten", "language": "Språket du vil angi", "reset": "<PERSON><PERSON> språket tilbake til standardspråket"}}, "prefix": {"description": "Viser eller angir botens prefiks", "errors": {"prefix_too_long": "Prefikset kan ikke være lengre enn 3 tegn."}, "messages": {"current_prefix": "Prefikset for denne serveren er `{prefix}`", "prefix_set": "Prefikset for denne serveren er nå `{prefix}`", "prefix_reset": "Prefikset for denne serveren er nå `{prefix}`"}, "options": {"set": "<PERSON><PERSON>", "prefix": "Prefikset du vil angi", "reset": "Tilbakestiller prefikset til standard"}}, "setup": {"description": "<PERSON><PERSON> opp boten", "errors": {"channel_exists": "Sangforespørselkanalen finnes allerede.", "channel_not_exists": "Sangforespørselkanalen finnes ikke.", "channel_delete_fail": "Sangforespørselkanalen er slettet. Hvis kanalen ikke slettes normalt, slett den selv."}, "messages": {"channel_created": "Sangforespørselkanalen er opprettet i <#{channelId}>.", "channel_deleted": "Sangforespørselkanalen er slettet.", "channel_info": "Sangforespørselk<PERSON><PERSON> er <#{channelId}>."}, "options": {"create": "<PERSON><PERSON><PERSON><PERSON>forespørselk<PERSON>len", "delete": "<PERSON><PERSON><PERSON> sang<PERSON><PERSON>p<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Viser sangforespørselkanalen"}}, "8d": {"description": "Slå på/av 8d-filteret", "messages": {"filter_enabled": "`✅` | 8D-filteret er `AKTIVERT`.", "filter_disabled": "`✅` | 8D-filteret er `DEAKTIVERT`."}}, "bassboost": {"description": "Slå på/av bassboost-filteret", "messages": {"filter_enabled": "`✅` | Bassboost-filteret er `AKTIVERT`. \n**<PERSON><PERSON><PERSON> forsiktig, å lytte for høyt kan skade hørselen!**", "filter_disabled": "`✅` | Bassboost-filteret er `DEAKTIVERT`."}, "options": {"level": "The bassboost level you want to set"}}, "distortion": {"description": "Slå på/av forvrengningsfilteret", "messages": {"filter_enabled": "`✅` | Forvrengningsfilteret er `AKTIVERT`.", "filter_disabled": "`✅` | Forvrengningsfilteret er `DEAKTIVERT`."}}, "karaoke": {"description": "Slå på/av karaokefilteret", "messages": {"filter_enabled": "`✅` | Karaokefilteret er `AKTIVERT`.", "filter_disabled": "`✅` | Karaokefilteret er `DEAKTIVERT`."}}, "lowpass": {"description": "Slå på/av lavpassfilteret", "messages": {"filter_enabled": "`✅` | Lavpassfilteret er `AKTIVERT`.", "filter_disabled": "`✅` | Lavpassfilteret er `DEAKTIVERT`."}}, "nightcore": {"description": "Slå på/av nightcore-filteret", "messages": {"filter_enabled": "`✅` | Nightcore-filteret er `AKTIVERT`.", "filter_disabled": "`✅` | Nightcore-filteret er `DEAKTIVERT`."}}, "pitch": {"description": "Slå på/av tone<PERSON>efilteret", "options": {"pitch": "Tallet du vil angi <PERSON> til (mellom 0,5 og 5)"}, "errors": {"invalid_number": "<PERSON><PERSON> et gyldig tall mellom 0,5 og 5."}, "messages": {"pitch_set": "`✅` | Tonehøyden er satt til **{pitch}**."}}, "rate": {"description": "<PERSON><PERSON> has<PERSON>n på sangen", "options": {"rate": "Tallet du vil angi hastigheten til (mellom 0,5 og 5)"}, "errors": {"invalid_number": "<PERSON><PERSON> et gyldig tall mellom 0,5 og 5."}, "messages": {"rate_set": "`✅` | Hastigheten er satt til **{rate}**."}}, "reset": {"description": "Tilbakestiller de aktive filtrene", "messages": {"filters_reset": "`✅` | Filtrene er tilbakestilt."}}, "rotation": {"description": "Slå på/av rotasjonsfilteret", "messages": {"enabled": "`✅` | Rotasjonsfilteret er `AKTIVERT`.", "disabled": "`✅` | Rotasjonsfilteret er `DEAKTIVERT`."}}, "speed": {"description": "<PERSON><PERSON> has<PERSON>n på sangen", "options": {"speed": "Has<PERSON>gh<PERSON>n du vil angi"}, "messages": {"invalid_number": "<PERSON><PERSON> et gyldig tall mellom 0,5 og 5.", "set_speed": "`✅` | Hastigheten er satt til **{speed}**."}}, "tremolo": {"description": "Slå på/av tremolofilteret", "messages": {"enabled": "`✅` | Tremolofilteret er `AKTIVERT`.", "disabled": "`✅` | Tremolofilteret er `DEAKTIVERT`."}}, "vibrato": {"description": "Slå på/av vibratofilteret", "messages": {"enabled": "`✅` | Vibratofilteret er `AKTIVERT`.", "disabled": "`✅` | Vibratofilteret er `DEAKTIVERT`."}}, "autoplay": {"description": "Veksler automatisk avspilling", "messages": {"enabled": "`✅` | Autoavspilling er `AKTIVERT`.", "disabled": "`✅` | Autoavspilling er `DEAKTIVERT`."}}, "clearqueue": {"description": "<PERSON><PERSON><PERSON> køen", "messages": {"cleared": "<PERSON><PERSON><PERSON> er tømt."}}, "grab": {"description": "<PERSON><PERSON> sangen som spilles av for øyeblikket til din DM", "content": "**Varighet:** {length}\n**Forespurt av:** <@{requester}>\n**<PERSON><PERSON>:** [<PERSON><PERSON><PERSON> her]({uri})", "check_dm": "Sjekk din DM.", "dm_failed": "Jeg kunne ikke sende deg en DM."}, "join": {"description": "Blir med i talekanalen", "already_connected": "Jeg er allerede tilkoblet <#{channelId}>.", "no_voice_channel": "Du må være i en talekanal for å bruke denne kommandoen.", "joined": "Ble med i <#{channelId}>."}, "leave": {"description": "<PERSON><PERSON><PERSON>", "left": "Forlot <#{channelId}>.", "not_in_channel": "Jeg er ikke i en talekanal."}, "loop": {"description": "<PERSON> gjeldende sang eller kø", "looping_song": "**<PERSON><PERSON> sangen.**", "looping_queue": "**<PERSON><PERSON> køen.**", "looping_off": "**Looping er nå av.**"}, "nowplaying": {"description": "Viser sangen som spilles av for øyeblikket", "now_playing": "Spilles nå", "track_info": "[{title}]({uri}) - Forespurt av: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "<PERSON><PERSON> sang", "successfully_paused": "<PERSON>en er pauset."}, "play": {"description": "Spiller av en sang fra YouTube, Spotify eller http", "options": {"song": "Sangen du vil spille av"}, "loading": "Laster...", "errors": {"search_error": "Det oppstod en feil under søket.", "no_results": "Fant ingen resultater.", "queue_too_long": "<PERSON><PERSON><PERSON> er for lang. Maksimal lengde er {maxQueueSize} sanger.", "playlist_too_long": "Spillelisten er for lang. Maksimal lengde er {maxPlaylistSize} sanger."}, "added_to_queue": "La til [{title}]({uri}) i køen.", "added_playlist_to_queue": "La til {length} sanger i køen."}, "playnext": {"description": "Legg til sangen som skal spilles av neste i køen", "options": {"song": "Sangen du vil spille av"}, "loading": "Laster...", "errors": {"search_error": "Det oppstod en feil under søket.", "no_results": "Fant ingen resultater.", "queue_too_long": "<PERSON><PERSON><PERSON> er for lang. Maksimal lengde er {maxQueueSize} sanger.", "playlist_too_long": "Spillelisten er for lang. Maksimal lengde er {maxPlaylistSize} sanger."}, "added_to_play_next": "La til [{title}]({uri}) for å spille av neste i køen.", "added_playlist_to_play_next": "La til {length} sanger for å spille av neste i køen."}, "queue": {"description": "Viser gjeldende kø", "now_playing": "Spilles nå: [{title}]({uri}) - Forespurt av: <@{requester}> - Varighet: `{duration}`", "live": "DIREKTE", "track_info": "{index}. [{title}]({uri}) - Forespurt av: <@{requester}> - Varighet: `{duration}`", "title": "<PERSON><PERSON>", "page_info": "Side {index} av {total}"}, "remove": {"description": "<PERSON><PERSON><PERSON> en sang fra køen", "options": {"song": "Sangnummeret du vil fjerne"}, "errors": {"no_songs": "Det er ingen sanger i køen.", "invalid_number": "<PERSON><PERSON> et gyldig sangnummer."}, "messages": {"removed": "<PERSON><PERSON><PERSON> sangnummer {songNumber} fra køen."}}, "replay": {"description": "Spiller gjeldende spor på nytt", "errors": {"not_seekable": "Kan ikke spille av dette sporet på nytt, da det ikke kan søkes i."}, "messages": {"replaying": "Spiller gjeldende spor på nytt."}}, "resume": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> sang", "errors": {"not_paused": "Spilleren er ikke pauset."}, "messages": {"resumed": "Gjenopptok spilleren."}}, "search": {"description": "<PERSON><PERSON><PERSON> etter en sang", "options": {"song": "<PERSON>en du vil søke etter"}, "errors": {"no_results": "Fant ingen resultater.", "search_error": "Det oppstod en feil under søket."}, "messages": {"added_to_queue": "La til [{title}]({uri}) i køen."}}, "seek": {"description": "Sø<PERSON> til et bestemt tidspunkt i sangen", "options": {"duration": "Varigheten du vil søke til"}, "errors": {"invalid_format": "Ugyldig tidsformat. Eksempler: seek 1m, seek 1h 30m", "not_seekable": "Det kan ikke søkes i dette sporet.", "beyond_duration": "Kan ikke søke utover sangens varighet på {length}."}, "messages": {"seeked_to": "<PERSON><PERSON><PERSON><PERSON> til {duration}"}}, "shuffle": {"description": "Stokker køen", "messages": {"shuffled": "Stokket køen."}}, "skip": {"description": "<PERSON> over <PERSON><PERSON><PERSON><PERSON> sang", "messages": {"skipped": "Hoppet over [{title}]({uri})."}}, "skipto": {"description": "<PERSON> til en bestemt sang i køen", "options": {"number": "Nummeret på sangen du vil hoppe til"}, "errors": {"invalid_number": "<PERSON><PERSON> et gyldig nummer."}, "messages": {"skipped_to": "Hoppet til sangnummer {number}."}}, "stop": {"description": "Stopper musikken og tømmer køen", "messages": {"stopped": "Stoppet musikken og tømte køen."}}, "volume": {"description": "<PERSON>ir volumet til spilleren", "options": {"number": "Volumet du vil angi"}, "messages": {"invalid_number": "<PERSON><PERSON> et gyldig tall.", "too_low": "Volumet kan ikke være lavere enn 0.", "too_high": "Volumet kan ikke være høyere enn 200. Vil du skade hørselen eller høyttalerne? Hmmm, jeg tror ikke det er en god idé.", "set": "Volumet er satt til {volume}"}}, "addsong": {"description": "<PERSON><PERSON> til en sang i spillelisten", "options": {"playlist": "Spillelisten du vil legge til i", "song": "Sangen du vil legge til"}, "messages": {"no_playlist": "<PERSON><PERSON> en spilleli<PERSON>", "no_song": "<PERSON><PERSON> en sang", "playlist_not_found": "Finner ikke den spillelisten", "no_songs_found": "<PERSON>t ingen sanger", "added": "La til {count} sang(er) i {playlist}"}}, "create": {"description": "Oppretter en spilleliste", "options": {"name": "Navnet på <PERSON>"}, "messages": {"name_too_long": "Spillelistenavn kan bare være 50 tegn lange.", "playlist_exists": "En spilleliste med det navnet finnes allerede. Bruk et annet navn.", "playlist_created": "<PERSON><PERSON><PERSON><PERSON>en **{name}** er opprettet."}}, "delete": {"description": "Sletter en spilleliste", "options": {"playlist": "S<PERSON><PERSON><PERSON>en du vil slette"}, "messages": {"playlist_not_found": "Finner ikke den spillelisten.", "playlist_deleted": "<PERSON><PERSON><PERSON> spill<PERSON> **{playlistName}**."}}, "list": {"description": "<PERSON><PERSON> alle spillelister for brukeren", "options": {"user": "Brukeren hvis spillelister du vil hente"}, "messages": {"no_playlists": "<PERSON>ne brukeren har ingen spillelister.", "your": "<PERSON>e", "playlists_title": "{username}s <PERSON><PERSON>ster", "error": "En feil oppstod under henting av spillelistene."}}, "load": {"description": "<PERSON>er en <PERSON>", "options": {"playlist": "Spillelisten du vil laste"}, "messages": {"playlist_not_exist": "Finner ikke den spillelisten.", "playlist_empty": "Den spillelisten er tom.", "playlist_loaded": "Lastet `{name}` med `{count}` sanger."}}, "removesong": {"description": "<PERSON><PERSON><PERSON> en sang fra spillelisten", "options": {"playlist": "Spillelisten du vil fjerne fra", "song": "Sangen du vil fjerne"}, "messages": {"provide_playlist": "<PERSON><PERSON> en spilleliste.", "provide_song": "<PERSON><PERSON> en sang.", "playlist_not_exist": "Finner ikke den spillelisten.", "song_not_found": "<PERSON>t ingen sams<PERSON><PERSON><PERSON> sang.", "song_removed": "F<PERSON><PERSON> {song} fra {playlist}.", "error_occurred": "En feil oppstod under fjerning av sangen."}}, "steal": {"description": "<PERSON><PERSON><PERSON> en spilleliste fra en annen bruker og legger den til i spillelistene dine", "options": {"playlist": "Spillelisten du vil stjele", "user": "Brukeren du vil stjele spillelisten fra"}, "messages": {"provide_playlist": "Angi et spillelistenavn.", "provide_user": "Nevn en bruker.", "playlist_not_exist": "Finner ikke den spillelisten for den nevnte brukeren.", "playlist_stolen": "<PERSON><PERSON><PERSON>{playlist}` fra {user}.", "error_occurred": "En feil oppstod under st<PERSON>ling av spillelisten."}}, "lyrics": {"description": "Get's the lyrics of the currently playing track"}}, "buttons": {"invite": "Inviter", "support": "Støtteserver", "previous": "<PERSON><PERSON><PERSON>", "resume": "Gjenopp<PERSON>", "stop": "Stopp", "skip": "<PERSON><PERSON> over", "loop": "Loop", "errors": {"not_author": "<PERSON> kan ikke bruke denne knappen."}}, "player": {"errors": {"no_player": "Det er ingen aktiv spiller i denne serveren.", "no_channel": "Du må være i en talekanal for å bruke denne kommandoen.", "queue_empty": "<PERSON><PERSON><PERSON> er tom.", "no_previous": "Det er ingen tidligere sanger i køen.", "no_song": "Det er ingen sanger i køen.", "already_paused": "Sangen er allerede pauset."}, "trackStart": {"now_playing": "Spilles nå", "requested_by": "Forespurt av {user}", "duration": "<PERSON><PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON>", "not_connected_to_voice_channel": "Du er ikke tilkoblet <#{channel}> for å bruke disse knappene.", "need_dj_role": "<PERSON> må ha DJ-rollen for å bruke denne kommandoen.", "previous_by": "<PERSON><PERSON>e av {user}", "no_previous_song": "Det er ingen forrige sang.", "paused_by": "Pauset av {user}", "resumed_by": "Gjenopptatt av {user}", "skipped_by": "Hoppet over av {user}", "no_more_songs_in_queue": "Det er ingen flere sanger i køen.", "looping_by": "Looper av {user}", "looping_queue_by": "<PERSON><PERSON> kø av {user}", "looping_off_by": "Slår av looping av {user}"}, "setupStart": {"now_playing": "Spilles nå", "description": "[{title}]({uri}) av {author} • `[{length}]` - Forespurt av <@{requester}>", "error_searching": "Det oppstod en feil under søket.", "no_results": "Fant ingen resultater.", "nothing_playing": "Ingen avspilling akkurat nå.", "queue_too_long": "<PERSON><PERSON><PERSON> er for lang. Maksimal lengde er {maxQueueSize} sanger.", "playlist_too_long": "Spillelisten er for lang. Maksimal lengde er {maxPlaylistSize} sanger.", "added_to_queue": "La til [{title}]({uri}) i køen.", "added_playlist_to_queue": "La til [{length}] sanger fra spillelisten i køen."}}, "event": {"interaction": {"setup_channel": "Du kan ikke bruke denne kommandoen i oppsettkanalen.", "no_send_message": "Jeg har ikke <PERSON> **`SendMessage`** i `{guild}`\nkanal: {channel}.", "no_embed_links": "Jeg har ikke till<PERSON> **`EmbedLinks`**.", "no_permission": "Jeg har ikke nok tillatelser til å utføre denne kommandoen.", "no_user_permission": "Du har ikke nok tillatelser til å bruke denne kommandoen.", "no_voice_channel": "Du må være koblet til en talekanal for å bruke denne `{command}`-kommandoen.", "no_connect_permission": "<PERSON>g har ikke `CONNECT`-tillatelsene til å utføre denne `{command}`-kommandoen.", "no_speak_permission": "<PERSON>g har ikke `SPEAK`-tillatels<PERSON> til å utføre denne `{command}`-kommandoen.", "no_request_to_speak": "Jeg har ikke <PERSON> `REQUEST TO SPEAK` til å utføre denne `{command}`-kommandoen.", "different_voice_channel": "Du er ikke tilkoblet {channel} for å bruke denne `{command}`-kommandoen.", "no_music_playing": "Ingenting spilles av akkurat nå.", "no_dj_role": "DJ-rollen er ikke angitt.", "no_dj_permission": "<PERSON> må ha DJ-rollen for å bruke denne kommandoen.", "cooldown": "Vent {time} sekund(er) til før du bruker `{command}`-kommandoen på nytt.", "error": "En feil oppstod: `{error}`"}, "message": {"prefix_mention": "Hei, prefikset mitt for denne serveren er `{prefix}`. Vil du ha mer informasjon? Bruk da `{prefix}help`\nHold deg trygg, hold deg fantastisk!", "no_send_message": "Jeg har ikke <PERSON> **`SendMessage`** i `{guild}`\nkanal: {channel}.", "no_embed_links": "Jeg har ikke till<PERSON> **`EmbedLinks`**.", "no_permission": "Jeg har ikke nok tillatelser til å utføre denne kommandoen.", "no_user_permission": "Du har ikke nok tillatelser til å bruke denne kommandoen.", "no_voice_channel": "Du må være koblet til en talekanal for å bruke denne `{command}`-kommandoen.", "no_connect_permission": "<PERSON>g har ikke `CONNECT`-tillatelsene til å utføre denne `{command}`-kommandoen.", "no_speak_permission": "<PERSON>g har ikke `SPEAK`-tillatels<PERSON> til å utføre denne `{command}`-kommandoen.", "no_request_to_speak": "Jeg har ikke <PERSON> `REQUEST TO SPEAK` til å utføre denne `{command}`-kommandoen.", "different_voice_channel": "Du er ikke tilkoblet {channel} for å bruke denne `{command}`-kommandoen.", "no_music_playing": "Ingenting spilles av akkurat nå.", "no_dj_role": "DJ-rollen er ikke angitt.", "no_dj_permission": "<PERSON> må ha DJ-rollen for å bruke denne kommandoen.", "missing_arguments": "Mangler argumenter", "missing_arguments_description": "<PERSON><PERSON> argumentene for `{command}`-kommandoen.\n\nEksempler:\n{examples}", "syntax_footer": "Syntaks: [] = valgfri, <> = obligatorisk", "cooldown": "Vent {time} sekund(er) til før du bruker `{command}`-kommandoen på nytt.", "no_mention_everyone": "Du kan ikke bruke denne kommandoen med everyone eller here.", "error": "En feil oppstod: `{error}`", "no_voice_channel_queue": "Du er ikke tilkoblet en talekanal for å legge til sanger i køen.", "no_permission_connect_speak": "Jeg har ikke nok tillatelse til å koble til/snakke i <#{channel}>.", "different_voice_channel_queue": "Du er ikke tilkoblet <#{channel}> for å legge til sanger i køen."}, "setupButton": {"no_voice_channel_button": "Du er ikke tilkoblet en talekanal for å bruke denne knappen.", "different_voice_channel_button": "Du er ikke tilkoblet {channel} for å bruke disse knappene.", "now_playing": "Spilles nå", "live": "DIREKTE", "requested_by": "Forespurt av <@{requester}>", "no_dj_permission": "<PERSON> må ha DJ-rollen for å bruke denne kommandoen.", "volume_set": "Volum satt til {vol}%", "volume_footer": "Volum: {vol}%", "paused": "<PERSON><PERSON><PERSON>", "resumed": "Gjenopptatt", "pause_resume": "{name} musikken.", "pause_resume_footer": "{name} av {displayName}", "no_music_to_skip": "Det er ingen musikk å hoppe over.", "skipped": "Hoppet over musikken.", "skipped_footer": "Hoppet over av {displayName}", "stopped": "Stoppet musikken.", "stopped_footer": "Stoppet av {displayName}", "nothing_playing": "Ingen avspilling akkurat nå", "loop_set": "Loop satt til {loop}.", "loop_footer": "Loop satt til {loop} av {displayName}", "shuffled": "Stokket køen.", "no_previous_track": "Det er ingen forrige spor.", "playing_previous": "Spiller av forrige spor.", "previous_footer": "Spiller av forrige spor av {displayName}", "rewinded": "Spolet musikken tilbake.", "rewind_footer": "Spolet tilbake av {displayName}", "forward_limit": "Du kan ikke spole musikken fremover mer enn lengden på sangen.", "forwarded": "Spolet musikken fremover.", "forward_footer": "Spolet fremover av {displayName}", "button_not_available": "<PERSON>ne knappen er ikke tilgjengelig.", "no_music_playing": "Det spilles ingen musikk på denne serveren."}}, "Evaluate code": "Evaluer kode", "Leave a guild": "Forlat en server", "List all guilds the bot is in": "List opp alle servere boten er på", "Restart the bot": "Start boten på nytt"}