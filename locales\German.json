{"cmd": {"247": {"description": "Lege den Bot so fest, dass er im Sprachkanal bleibt", "errors": {"not_in_voice": "<PERSON> musst in einem Sprachkanal sein, um diesen Befehl zu verwenden.", "generic": "Ein Fehler ist aufgetreten, als versucht wurde, diesen Be<PERSON>hl auszuführen."}, "messages": {"disabled": "`✅` | 24/7-<PERSON><PERSON> wurde `DEAKTIVIERT`", "enabled": "`✅` | 24/7-<PERSON><PERSON> wurde `AKTIVIERT`. \n**Der Bot verlässt den Sprachkanal nicht, auch wenn sich keine Personen im Sprachkanal befinden.**"}}, "ping": {"description": "<PERSON><PERSON><PERSON> den Ping des Bots an.", "content": "Ping...", "bot_latency": "Bot-<PERSON><PERSON>", "api_latency": "API-Latenz", "requested_by": "<PERSON><PERSON><PERSON><PERSON> <PERSON> {author}"}, "lavalink": {"description": "Zeigt die aktuellen Lavalink-Statistiken an.", "title": "Lavalink-Statistiken", "content": "Spieler: {players}\n<PERSON><PERSON><PERSON><PERSON> Spieler: {playingPlayers}\nBetriebszeit: {uptime}\nKerne: {cores} Kern(e)\nSpeichernutzung: {used} / {reservable}\nSystemlast: {systemLoad}%\nLavalink-Last: {lavalinkLoad}%"}, "invite": {"description": "Hole den Einladungslink des Bots.", "content": "Du kannst mich einladen, indem du auf den unten stehenden Button klickst. Bugs oder Ausfälle? Trete dem Support-Server bei!"}, "help": {"description": "<PERSON><PERSON><PERSON> Hilfe-Menü an.", "options": {"command": "<PERSON>hl, zu dem du Informationen erhalten möchtest"}, "content": "Hallo! Ich bin {bot}, e<PERSON>, der mit [Lavamusic](https://github.com/appujet/lavamusic) und Discord erstellt wurde. Du kannst `{prefix}help <command>` ve<PERSON><PERSON><PERSON>, um mehr Informationen zu einem Befehl zu erhalten.", "title": "<PERSON>lf<PERSON>-<PERSON>ü", "not_found": "<PERSON><PERSON> `{cmdName}` existiert nicht.", "help_cmd": "**Beschreibung:** {description}\n**Verwendung:** {usage}\n**Beispiele:** {examples}\n**Alias:** {aliases}\n**Kategorie:** {category}\n**Abklingzeit:** {cooldown} Sekunden\n**Berechtigungen:** {premUser}\n**Bot-Berechtigungen:** {premBot}\n**Nur für Entwickler:** {dev}\n**Slash-<PERSON><PERSON>hl:** {slash}\n**Args:** {args}\n**<PERSON><PERSON><PERSON>:** {player}\n**DJ:** {dj}\n**DJ-Berechtigungen:** {djPerm}\n**Stimme:** {voice}", "footer": "Verwen<PERSON> `{prefix}help <command>` für weitere Informationen zu einem Befehl"}, "botinfo": {"description": "Informationen über den Bot", "content": "Bot-Informationen:\n- **Betriebssystem**: {osInfo}\n- **Betriebszeit**: {osUptime}\n- **Hostname**: {osHostname}\n- **CPU-Architektur**: {cpuInfo}\n- **CPU-Auslastung**: {cpuUsed}%\n- **Speichernutzung**: {memUsed}MB / {memTotal}GB\n- **Node-Version**: {nodeVersion}\n- **Discord-Version**: {discordJsVersion}\n- **Verbunden mit** {guilds} Guilden, {channels} Kanälen und {users} Benutzern\n- **Gesamtzahl der Befehle**: {commands}"}, "about": {"description": "Zeigt Informationen über den Bot an", "fields": {"creator": "<PERSON><PERSON><PERSON>", "repository": "Repository", "support": "Support", "description": "Er wollte unbedingt sein erstes Open-Source-Projekt überhaupt für mehr Programmiererfahrung erstellen. In diesem Projekt wurde er herausgefordert, ein Projekt mit weniger Bugs zu erstellen. <PERSON><PERSON> hoffe, du hast Spaß beim Verwenden von LavaMusic!"}}, "dj": {"description": "Verwalte den DJ-Modus und die zugehörigen Rollen", "errors": {"provide_role": "Bitte gib eine Rolle an.", "no_roles": "Die DJ-<PERSON>e ist leer.", "invalid_subcommand": "Bitte gib einen gültigen Unterbefehl an."}, "messages": {"role_exists": "Die DJ-Rolle <@&{roleId}> wurde bereits hinzugefügt.", "role_added": "Die DJ-Rolle <@&{roleId}> wurde hinzugefügt.", "role_not_found": "Die DJ-Rolle <@&{roleId}> wurde nicht hinzugefügt.", "role_removed": "Die DJ-<PERSON>e <@&{roleId}> wurde entfernt.", "all_roles_cleared": "Alle DJ-<PERSON><PERSON> wurden entfernt.", "toggle": "Der DJ-<PERSON><PERSON> wurde auf {status} umgeschaltet."}, "options": {"add": "Die DJ-Rolle, die du hinzufügen möchtest", "remove": "Die DJ-<PERSON>e, die du entfernen möchtest", "clear": "Lösche alle DJ-Rollen", "toggle": "<PERSON>halte die DJ-Rolle um", "role": "Die DJ-Rolle"}, "subcommands": "Unterbefehle"}, "prefix": {"description": "<PERSON><PERSON><PERSON> oder setzt das Präfix des Bots", "errors": {"prefix_too_long": "Das Präfix darf nicht länger als 3 Zeichen sein."}, "messages": {"current_prefix": "Das Präfix für diesen Server ist `{prefix}`", "prefix_set": "Das Präfix für diesen Server ist jetzt `{prefix}`", "prefix_reset": "Das Präfix für diesen Server ist jetzt `{prefix}`"}, "options": {"set": "Setzt das Präfix", "prefix": "Das Präfix, das du setzen möchtest", "reset": "Setzt das Präfix auf das Standardpräfix zurück"}}, "setup": {"description": "<PERSON><PERSON> den Bo<PERSON> ein", "errors": {"channel_exists": "Der Song-Anforderungskanal existiert bereits.", "channel_not_exists": "Der Song-Anforderungskanal existiert nicht.", "channel_delete_fail": "Der Song-Anforderungskanal wurde gelöscht. Wenn der Kanal nicht normal gelöscht wird, lösche ihn bitte selbst."}, "messages": {"channel_created": "<PERSON> Song-Anforderungskanal wurde in <#{channelId}> erstellt.", "channel_deleted": "Der Song-Anforderungskanal wurde gelöscht.", "channel_info": "<PERSON> Song-Anforderungskanal ist <#{channelId}>."}, "options": {"create": "<PERSON><PERSON><PERSON>-Anforderungskanal", "delete": "Lösche den Song-Anforderungskanal", "info": "<PERSON><PERSON><PERSON>-Anforderungskanal an"}}, "8d": {"description": "8d-Filter ein-/ausschalten", "messages": {"filter_enabled": "`✅` | 8D-Filter wurde `AKTIVIERT`.", "filter_disabled": "`✅` | 8D-Filter wurde `DEAKTIVIERT`."}}, "bassboost": {"description": "Bassboost-Filter ein-/ausschalten", "messages": {"filter_enabled": "`✅` | Bassboost-<PERSON>lter wurde `AKTIVIERT`. \n**<PERSON><PERSON><PERSON><PERSON>, zu lautes Hören kann deine Ohren schädigen!**", "filter_disabled": "`✅` | Bassboost-Filter wurde `DEAKTIVIERT`."}, "options": {"level": "The bassboost level you want to set"}}, "distortion": {"description": "Schalte den Verzerrungsfilter ein-/aus", "messages": {"filter_enabled": "`✅` | Verzerrungsfilter wurde `AKTIVIERT`.", "filter_disabled": "`✅` | Verzerrungsfilter wurde `DEAKTIVIERT`."}}, "karaoke": {"description": "<PERSON><PERSON><PERSON> den Karaoke-<PERSON>lter ein-/aus", "messages": {"filter_enabled": "`✅` | Karaoke-Filter wurde `AKTIVIERT`.", "filter_disabled": "`✅` | Karaoke-Filter wurde `DEAKTIVIERT`."}}, "lowpass": {"description": "<PERSON><PERSON><PERSON> den Tiefpassfilter ein-/aus", "messages": {"filter_enabled": "`✅` | Tiefpassfilter wurde `AKTIVIERT`.", "filter_disabled": "`✅` | Tiefpassfilter wurde `DEAKTIVIERT`."}}, "nightcore": {"description": "<PERSON><PERSON><PERSON> den Nightcore-<PERSON><PERSON> ein-/aus", "messages": {"filter_enabled": "`✅` | Nightcore-Filter wurde `AKTIVIERT`.", "filter_disabled": "`✅` | Nightcore-Filter wurde `DEAKTIVIERT`."}}, "pitch": {"description": "<PERSON><PERSON><PERSON> den Pitch-Filter ein-/aus", "options": {"pitch": "<PERSON> Zahl, auf die du den Pitch setzen möchtest (zwischen 0,5 und 5)"}, "errors": {"invalid_number": "<PERSON>te gib eine gültige Zahl zwischen 0,5 und 5 ein."}, "messages": {"pitch_set": "`✅` | Pitch wurde auf **{pitch}** gesetzt."}}, "rate": {"description": "Ändere die Rate des Songs", "options": {"rate": "<PERSON> Zahl, auf die du die Rate setzen möchtest (zwischen 0,5 und 5)"}, "errors": {"invalid_number": "<PERSON>te gib eine gültige Zahl zwischen 0,5 und 5 ein."}, "messages": {"rate_set": "`✅` | Rate wurde auf **{rate}** gesetzt."}}, "reset": {"description": "Setzt die aktiven Filter zurück", "messages": {"filters_reset": "`✅` | <PERSON>lter wurden zurückgesetzt."}}, "rotation": {"description": "Schalte den Rotationsfilter ein-/aus", "messages": {"enabled": "`✅` | Rotationsfilter wurde `AKTIVIERT`.", "disabled": "`✅` | Rotationsfilter wurde `DEAKTIVIERT`."}}, "speed": {"description": "Ändere die Geschwindigkeit des Songs", "options": {"speed": "Die Geschwindigkeit, die du einstellen möchtest"}, "messages": {"invalid_number": "<PERSON>te gib eine gültige Zahl zwischen 0,5 und 5 ein.", "set_speed": "`✅` | Geschwindigkeit wurde auf **{speed}** gesetzt."}}, "tremolo": {"description": "<PERSON><PERSON><PERSON> den Tremolo-Filter ein-/aus", "messages": {"enabled": "`✅` | Tremolo-Filter wurde `AKTIVIERT`.", "disabled": "`✅` | Tremolo-Filter wurde `DEAKTIVIERT`."}}, "vibrato": {"description": "<PERSON><PERSON><PERSON> den Vibrato-Filter ein-/aus", "messages": {"enabled": "`✅` | Vibrato-Filter wurde `AKTIVIERT`.", "disabled": "`✅` | Vibrato-Filter wurde `DEAKTIVIERT`."}}, "autoplay": {"description": "Schaltet Autoplay um", "messages": {"enabled": "`✅` | Autoplay wurde `AKTIVIERT`.", "disabled": "`✅` | Autoplay wurde `DEAKTIVIERT`."}}, "clearqueue": {"description": "<PERSON><PERSON> die Warteschlange", "messages": {"cleared": "Die Warteschlange wurde geleert."}}, "grab": {"description": "Holt den aktuell abgespielten Song in deine DM", "content": "**Dauer:** {length}\n**<PERSON><PERSON><PERSON><PERSON> von:** <@{requester}>\n**Link:** [<PERSON><PERSON><PERSON> hier]({uri})", "check_dm": "Bitte überprüfe deine DM.", "dm_failed": "<PERSON>ch konnte dir keine DM senden."}, "join": {"description": "<PERSON>tt dem Sprachkanal bei", "already_connected": "Ich bin bereits mit <#{channelId}> verbunden.", "no_voice_channel": "<PERSON> musst in einem Sprachkanal sein, um diesen Befehl zu verwenden.", "joined": "Erfolgreich <#{channelId}> beigetreten."}, "leave": {"description": "Verlässt den Sprachkanal", "left": "Erfolgreich <#{channelId}> verlassen.", "not_in_channel": "<PERSON>ch bin in keinem Sprachkanal."}, "loop": {"description": "Wiederhole den aktuellen Song oder die Warteschlange", "looping_song": "**<PERSON><PERSON><PERSON><PERSON> den Song.**", "looping_queue": "**Wiederhole die Warteschlange.**", "looping_off": "**Wiederholung ist jetzt deaktiviert.**"}, "nowplaying": {"description": "Zeigt den aktuell abgespielten Song an", "now_playing": "Jetzt wird abgespielt", "track_info": "[{title}]({uri}) - <PERSON><PERSON><PERSON><PERSON> <PERSON>: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "Pausiert den aktuellen Song", "successfully_paused": "Song erfolg<PERSON>ich pausiert."}, "play": {"description": "Spielt einen Song von YouTube, Spotify oder http ab", "options": {"song": "<PERSON> Song, den du abspielen möchtest"}, "loading": "Lädt...", "errors": {"search_error": "Ein Fehler ist beim Suchen aufgetreten.", "no_results": "<PERSON>s wurden keine Ergebnisse gefunden.", "queue_too_long": "Die Warteschlange ist zu lang. Die maximale Länge beträgt {maxQueueSize} Songs.", "playlist_too_long": "Die Playlist ist zu lang. Die maximale Länge beträgt {maxPlaylistSize} Songs."}, "added_to_queue": "[{title}]({uri}) zur Warteschlange hinzugefügt.", "added_playlist_to_queue": "{length} Songs zur Warteschlange hinzugefügt."}, "playnext": {"description": "<PERSON><PERSON><PERSON> den Song hinzu, der als nächstes in der Warteschlange abgespielt werden soll", "options": {"song": "<PERSON> Song, den du abspielen möchtest"}, "loading": "Lädt...", "errors": {"search_error": "Ein Fehler ist beim Suchen aufgetreten.", "no_results": "<PERSON>s wurden keine Ergebnisse gefunden.", "queue_too_long": "Die Warteschlange ist zu lang. Die maximale Länge beträgt {maxQueueSize} Songs.", "playlist_too_long": "Die Playlist ist zu lang. Die maximale Länge beträgt {maxPlaylistSize} Songs."}, "added_to_play_next": "[{title}]({uri}) zur nächsten Wiedergabe in der Warteschlange hinzugefügt.", "added_playlist_to_play_next": "{length} Songs zur nächsten Wiedergabe in der Warteschlange hinzugefügt."}, "queue": {"description": "Zeigt die aktuelle Warteschlange an", "now_playing": "Wird gerade abgespielt: [{title}]({uri}) - <PERSON><PERSON><PERSON><PERSON> <PERSON>: <@{requester}> - <PERSON><PERSON>: `{duration}`", "live": "LIVE", "track_info": "{index}. [{title}]({uri}) - <PERSON><PERSON><PERSON><PERSON> <PERSON>: <@{requester}> - <PERSON><PERSON>: `{duration}`", "title": "Warteschlange", "page_info": "Seite {index} von {total}"}, "remove": {"description": "Entfernt einen Song aus der Warteschlange", "options": {"song": "Die Songnummer, die du entfernen möchtest"}, "errors": {"no_songs": "Es befinden sich keine Songs in der Warteschlange.", "invalid_number": "<PERSON>te gib eine gültige Songnummer ein."}, "messages": {"removed": "Song Nummer {songNumber} aus der Warteschlange entfernt."}}, "replay": {"description": "Spielt den aktuellen Track erneut ab", "errors": {"not_seekable": "Dieser Track kann nicht erneut abgespielt werden, da er nicht suchbar ist."}, "messages": {"replaying": "Spiele den aktuellen Track erneut ab."}}, "resume": {"description": "Setzt den aktuellen Song fort", "errors": {"not_paused": "Der Player ist nicht pausiert."}, "messages": {"resumed": "Player fortgesetzt."}}, "search": {"description": "<PERSON>t nach einem Song", "options": {"song": "<PERSON> Song, nach dem du suchen möchtest"}, "errors": {"no_results": "<PERSON><PERSON> gefunden.", "search_error": "Ein Fehler ist beim Suchen aufgetreten."}, "messages": {"added_to_queue": "[[{title}]({uri})] zur Warteschlange hinzugefügt."}}, "seek": {"description": "<PERSON><PERSON> zu einer bestimmten Zeit im Song", "options": {"duration": "<PERSON> Dauer, zu der du springen möchtest"}, "errors": {"invalid_format": "Ungültiges Zeitformat. Beispiele: seek 1m, seek 1h 30m", "not_seekable": "Dieser Track ist nicht suchbar.", "beyond_duration": "Kann nicht über die Songdauer von {length} hinaus springen."}, "messages": {"seeked_to": "Gesprungen zu {duration}"}}, "shuffle": {"description": "Mische die Warteschlange", "messages": {"shuffled": "Warteschlange gemischt."}}, "skip": {"description": "Überspringe den aktuellen Song", "messages": {"skipped": "Übersprungen [{title}]({uri})."}}, "skipto": {"description": "Überspringe zu einem bestimmten Song in der Warteschlange", "options": {"number": "Die Nummer des Songs, zu dem du springen möchtest"}, "errors": {"invalid_number": "<PERSON>te gib eine gültige Nummer ein."}, "messages": {"skipped_to": "Gesprungen zu <PERSON> {number}."}}, "stop": {"description": "Stoppt die Musik und leert die Warteschlange", "messages": {"stopped": "Musik gestoppt und Warteschlange geleert."}}, "volume": {"description": "Stellt die Lautstärke des Players ein", "options": {"number": "Die Lautstärke, die du einstellen möchtest"}, "messages": {"invalid_number": "<PERSON>te gib eine gültige Nummer ein.", "too_low": "Die Lautstärke kann nicht unter 0 liegen.", "too_high": "Die Lautstärke kann nicht über 200 liegen. Willst du dein Gehör oder deine Lautsprecher beschädigen? Hmmm, ich glaube nicht, dass das eine so gute Idee ist.", "set": "Lautstärke auf {volume} gesetzt"}}, "addsong": {"description": "<PERSON><PERSON><PERSON> einen Song zur Playlist hinzu", "options": {"playlist": "Die Playlist, zu der du den Song hinzufügen möchtest", "song": "Der Song, den du hinzufügen möchtest"}, "messages": {"no_playlist": "<PERSON><PERSON> gib eine Playlist an", "no_song": "<PERSON>te gib einen Song an", "playlist_not_found": "Diese Playlist existiert nicht", "no_songs_found": "Keine Songs gefunden", "added": "{count} Song(s) zu {playlist} hinz<PERSON>fügt"}}, "create": {"description": "<PERSON><PERSON><PERSON> eine Playlist", "options": {"name": "Der Name der Playlist"}, "messages": {"name_too_long": "Playlist-<PERSON><PERSON> dürfen nur 50 <PERSON>ei<PERSON> lang sein.", "playlist_exists": "Eine Playlist mit diesem Namen existiert bereits. Bitte verwende einen anderen Namen.", "playlist_created": "Playlist **{name}** wurde erstellt."}}, "delete": {"description": "Lösche eine Playlist", "options": {"playlist": "Die Playlist, die du löschen möchtest"}, "messages": {"playlist_not_found": "Diese Playlist existiert nicht.", "playlist_deleted": "Playlist **{playlistName}** gelöscht."}}, "list": {"description": "Ruft alle Playlists für den Benutzer ab", "options": {"user": "<PERSON>, dessen Playlists du abrufen möchtest"}, "messages": {"no_playlists": "<PERSON><PERSON> hat keine Playlists.", "your": "<PERSON><PERSON>", "playlists_title": "Playlists von {username}", "error": "Ein Fehler ist beim Abrufen der Playlists aufgetreten."}}, "load": {"description": "<PERSON><PERSON><PERSON> eine Playlist", "options": {"playlist": "Die Playlist, die du laden möchtest"}, "messages": {"playlist_not_exist": "Diese Playlist existiert nicht.", "playlist_empty": "<PERSON><PERSON> Playlist ist leer.", "playlist_loaded": "Lade `{name}` mit `{count}` Songs."}}, "removesong": {"description": "Entfernt einen Song aus der Playlist", "options": {"playlist": "Die Playlist, aus der du den Song entfernen möchtest", "song": "Der Song, den du entfernen möchtest"}, "messages": {"provide_playlist": "<PERSON><PERSON> gib eine Playlist an.", "provide_song": "<PERSON>te gib einen Song an.", "playlist_not_exist": "Diese Playlist existiert nicht.", "song_not_found": "<PERSON><PERSON> passender Song gefunden.", "song_removed": "{song} aus {playlist} entfernt.", "error_occurred": "Ein Fehler ist beim Entfernen des Songs aufgetreten."}}, "steal": {"description": "<PERSON><PERSON><PERSON><PERSON> eine Playlist von einem andere<PERSON> und fügt sie deinen Playlists hinzu", "options": {"playlist": "Die Playlist, die du stehlen möchtest", "user": "<PERSON>, von dem du die Playlist stehlen möchtest"}, "messages": {"provide_playlist": "<PERSON><PERSON> gib einen Playlist-Namen an.", "provide_user": "Bitte erwähne einen Benutzer.", "playlist_not_exist": "Diese Playlist existiert für den genannten Benutzer nicht.", "playlist_stolen": "Playlist `{playlist}` er<PERSON><PERSON><PERSON><PERSON><PERSON> von {user} gestohlen.", "error_occurred": "Ein Fehler ist beim Stehlen der Playlist aufgetreten."}}, "language": {"description": "Legt die Sprache für den Bot fest", "invalid_language": "Bitte gib eine gültige Sprache an. Beispiel: `EnglishUS` für Englisch (Vereinigte Staaten)\n\nDie Liste der unterstützten Sprachen findest du [hier](https://discord.com/developers/docs/reference#locales)\n\n**Verfügbare Sprachen:**\n{languages}", "already_set": "Die Sprache ist bereits auf `{language}` gesetzt", "not_set": "Die Sprache wurde nicht festgelegt", "set": "`✅` | Die Sprache wurde auf `{language}` gesetzt", "reset": "`✅` | Die Sprache wurde auf die Standardsprache zurückgesetzt", "options": {"set": "Legt die Sprache für den Bot fest", "language": "Die Sprache, die du einstellen möchtest", "reset": "Ändere die Sprache zurück zur Standardsprache"}}, "lyrics": {"description": "Get's the lyrics of the currently playing track"}, "fairplay": {"description": "Set the bot to play music fairly"}, "playlocal": {"description": "Plays an your local audio files", "options": {"file": "The audio file you want to play"}}, "say": {"description": "Quick text to speech conversion", "options": {"text": "The text to say"}}, "speak": {"description": "Convert text to speech with advanced voice options", "options": {"text": "The text to convert to speech", "voice": "The voice to use for speech synthesis", "speed": "The speed of speech (0.5 to 2.0)"}}}, "buttons": {"invite": "Einladen", "support": "Support-Server", "previous": "<PERSON><PERSON><PERSON><PERSON>", "resume": "Fortsetzen", "stop": "Stoppen", "skip": "Überspringen", "loop": "<PERSON><PERSON><PERSON><PERSON>", "errors": {"not_author": "Du kannst diesen Button nicht verwenden."}}, "player": {"errors": {"no_player": "<PERSON><PERSON> gibt keinen aktiven Player in dieser Gilde.", "no_channel": "<PERSON> musst in einem Sprachkanal sein, um diesen Befehl zu verwenden.", "queue_empty": "Die Warteschlange ist leer.", "no_previous": "Es gibt keine vorherigen Songs in der Warteschlange.", "no_song": "Es befinden sich keine Songs in der Warteschlange.", "already_paused": "Der Song ist bereits pausiert."}, "trackStart": {"now_playing": "Jetzt wird abgespielt", "requested_by": "<PERSON><PERSON><PERSON><PERSON> <PERSON> {user}", "duration": "<PERSON><PERSON>", "author": "Autor", "not_connected_to_voice_channel": "Du bist nicht mit <#{channel}> verbunden, um diese Buttons zu verwenden.", "need_dj_role": "Du musst die DJ-<PERSON><PERSON> haben, um diesen Befehl zu verwenden.", "previous_by": "<PERSON><PERSON><PERSON><PERSON> {user}", "no_previous_song": "<PERSON>s gibt keinen vorherigen Song.", "paused_by": "<PERSON><PERSON><PERSON><PERSON> von {user}", "resumed_by": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {user}", "skipped_by": "<PERSON>bersprung<PERSON> von {user}", "no_more_songs_in_queue": "<PERSON>s gibt keinen weiteren Song in der Warteschlange.", "looping_by": "<PERSON><PERSON><PERSON><PERSON> {user}", "looping_queue_by": "Warteschlange in Schleife von {user}", "looping_off_by": "Schleife aus von {user}"}, "setupStart": {"now_playing": "Now Playing", "description": "[{title}]({uri}) by {author} • `[{length}]` - Requested by <@{requester}>", "error_searching": "There was an error while searching.", "no_results": "There were no results found.", "nothing_playing": "Nothing playing right now.", "queue_too_long": "The queue is too long. The maximum length is {maxQueueSize} songs.", "playlist_too_long": "The playlist is too long. The maximum length is {maxPlaylistSize} songs.", "added_to_queue": "Added [{title}]({uri}) to the queue.", "added_playlist_to_queue": "Added [{length}] songs from the playlist to the queue."}}, "event": {"interaction": {"setup_channel": "Du kannst diesen Befehl nicht im Einrichtungskanal verwenden.", "no_send_message": "Ich habe keine **`SendMessage`**-Berechtigung in `{guild}`\nKanal: {channel}.", "no_embed_links": "Ich habe keine **`EmbedLinks`**-Berechtigung.", "no_permission": "Ich habe nicht genügend Berechtigungen, um diesen Befehl auszuführen.", "no_user_permission": "Du hast nicht genügend Berechtigungen, um diesen Befehl zu verwenden.", "no_voice_channel": "Du musst mit einem Sprachkanal verbunden sein, um diesen Be<PERSON>hl `{command}` zu verwenden.", "no_connect_permission": "Ich habe keine `CONNECT`-Berechtigungen, um diesen Befehl `{command}` auszuführen.", "no_speak_permission": "Ich habe keine `SPEAK`-Berechtigungen, um diesen Befehl `{command}` auszuführen.", "no_request_to_speak": "Ich habe keine `REQUEST TO SPEAK`-Berechtigung, um diesen Befehl `{command}` auszuführen.", "different_voice_channel": "Du bist nicht mit {channel} verbunden, um diesen Befehl `{command}` zu verwenden.", "no_music_playing": "Es wird gerade nichts abgespielt.", "no_dj_role": "DJ-<PERSON><PERSON> ist nicht gesetzt.", "no_dj_permission": "Du musst die DJ-<PERSON><PERSON> haben, um diesen Befehl zu verwenden.", "cooldown": "Bitte warte {time} weitere Sekunde(n), bevor du <PERSON> `{command}` erneut verwendest.", "error": "Ein Fehler ist aufgetreten: `{error}`"}, "message": {"prefix_mention": "Hey, mein Präfix für diesen Server ist `{prefix}`. Möchtest du mehr Infos? Dann gib `{prefix}help` ein\nBleib sicher, bleib großartig!", "no_send_message": "Ich habe keine **`SendMessage`**-Berechtigung in `{guild}`\nKanal: {channel}.", "no_embed_links": "Ich habe keine **`EmbedLinks`**-Berechtigung.", "no_permission": "Ich habe nicht genügend Berechtigungen, um diesen Befehl auszuführen.", "no_user_permission": "Du hast nicht genügend Berechtigungen, um diesen Befehl zu verwenden.", "no_voice_channel": "Du musst mit einem Sprachkanal verbunden sein, um diesen Be<PERSON>hl `{command}` zu verwenden.", "no_connect_permission": "Ich habe keine `CONNECT`-Berechtigungen, um diesen Befehl `{command}` auszuführen.", "no_speak_permission": "Ich habe keine `SPEAK`-Berechtigungen, um diesen Befehl `{command}` auszuführen.", "no_request_to_speak": "Ich habe keine `REQUEST TO SPEAK`-Berechtigung, um diesen Befehl `{command}` auszuführen.", "different_voice_channel": "Du bist nicht mit {channel} verbunden, um diesen Befehl `{command}` zu verwenden.", "no_music_playing": "Es wird gerade nichts abgespielt.", "no_dj_role": "DJ-<PERSON><PERSON> ist nicht gesetzt.", "no_dj_permission": "Du musst die DJ-<PERSON><PERSON> haben, um diesen Befehl zu verwenden.", "missing_arguments": "Fehlende Argumente", "missing_arguments_description": "Bitte gib die benötigten Argumente für den Befehl `{command}` an.\n\n<PERSON>:\n{examples}", "syntax_footer": "Syntax: [] = optional, <> = erforderlich", "cooldown": "Bitte warte {time} weitere Sekunde(n), bevor du <PERSON> `{command}` erneut verwendest.", "no_mention_everyone": "Du kannst diesen Be<PERSON>hl nicht mit @everyone oder @here verwenden.", "error": "Ein Fehler ist aufgetreten: `{error}`", "no_voice_channel_queue": "Du bist nicht mit einem Sprachkanal verbunden, um Songs in die Warteschlange zu stellen.", "no_permission_connect_speak": "Ich habe nicht genügend Berechtigungen, um mich in <#{channel}> zu verbinden/sprechen.", "different_voice_channel_queue": "Du bist nicht mit <#{channel}> verbunden, um Songs in die Warteschlange zu stellen."}, "setupButton": {"no_voice_channel_button": "Du bist nicht mit einem Sprachkanal verbunden, um diesen But<PERSON> zu verwenden.", "different_voice_channel_button": "Du bist nicht mit {channel} verbunden, um diese Buttons zu verwenden.", "now_playing": "Jetzt wird abgespielt", "live": "LIVE", "requested_by": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <@{requester}>", "no_dj_permission": "Du musst die DJ-<PERSON><PERSON> haben, um diesen Befehl zu verwenden.", "volume_set": "Lautstärke auf {vol}% gesetzt", "volume_footer": "Lautstärke: {vol}%", "paused": "<PERSON><PERSON><PERSON><PERSON>", "resumed": "Fortgesetzt", "pause_resume": "{name} die <PERSON><PERSON>.", "pause_resume_footer": "{name} von {displayName}", "no_music_to_skip": "<PERSON>s gibt keine Musik zum Überspringen.", "skipped": "Musik übersprungen.", "skipped_footer": "Übersprungen von {displayName}", "stopped": "Musik gestoppt.", "stopped_footer": "Gest<PERSON><PERSON> von {displayName}", "nothing_playing": "Es wird gerade nichts abgespielt", "loop_set": "<PERSON>hleife auf {loop} gesetzt.", "loop_footer": "<PERSON><PERSON><PERSON><PERSON> auf {loop} gese<PERSON><PERSON> von {displayName}", "shuffled": "Warteschlange gemischt.", "no_previous_track": "<PERSON>s gibt keinen vorherigen Track.", "playing_previous": "Spiele den vorherigen Track ab.", "previous_footer": "Spiele den vorherigen Track ab von {displayName}", "rewinded": "Musik zurückgespult.", "rewind_footer": "Zurückgespult von {displayName}", "forward_limit": "Du kannst die Musik nicht mehr als die Länge des Songs vorspulen.", "forwarded": "Musik vorgespult.", "forward_footer": "Vorgespult von {displayName}", "button_not_available": "<PERSON><PERSON> ist nicht verfügbar.", "no_music_playing": "Es wird gerade nichts abgespielt."}}, "Evaluate code": "Evaluate code", "Leave a guild": "Leave a guild", "List all guilds the bot is in": "List all guilds the bot is in", "Restart the bot": "Restart the bot", "The loop mode you want to set": "The loop mode you want to set"}